<script lang='ts'>
import { defineComponent, useAttrs, computed } from 'vue';
import PimaModal from './pima-modal.vue';

// eslint-disable-next-line import/order
import { namespaceT } from '@/helps/namespace-t';

export default defineComponent({
  name: 'ModalWarnning',

  components: {
    PimaModal,
  },

  props: {
    width: {
      type: Number,
      default: 488,
    },
    title: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      default: null,
    },

    loading: Boolean,

    customFooter: {
      type: Boolean,
      default: false,
    },
    hiddenIcon: {
      type: Boolean,
      default: false,
    },
    horizontal: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    'on-cancel',
    'on-confirm',
  ],

  setup(props, { emit }) {
    function onCancel() {
      emit('on-cancel');
    }

    function onConfirm() {
      emit('on-confirm');
    }

    function onVisibleChange(flag) {
      if (!flag) {
        onCancel();
      }
    }

    const visibleValue = computed(() => props.visible);

    return {
      attrs: useAttrs(),
      t: namespaceT(),
      onConfirm,
      onVisibleChange,
      onCancel,
      visibleValue,
    };
  },
});
</script>

<template>
  <PimaModal
    v-model="visibleValue"
    :width="width"
    :mask-closable="false"
    :closable="true"
    :title="title"
    class="pima-modal-wrapper"
    @on-visible-change="onVisibleChange"
  >
    <div
      class="content"
      :class="{
        'horizontal': horizontal,
      }"
    >
      <img
        v-if="!hiddenIcon"
        class="icon-warnning"
        src="@/assets/img/icon/warining-icon.png"
      >

      <div class="description">
        {{ description }}
      </div>
    </div>

    <template #footer>
      <div
        v-if="!customFooter"
        class="action"
      >
        <Button
          type="default"
          class="pima-btn"
          @click="onCancel()"
        >
          {{ t('common.action.cancel') }}
        </Button>
        <Button
          type="primary"
          class="button-primary pima-btn"
          :loading="loading"
          @click="onConfirm()"
        >
          {{ t('common.action.confirm') }}
        </Button>
      </div>

      <slot
        v-else
        name="button"
      />
    </template>
  </PimaModal>
</template>


<style lang="less" scoped>
:deep(.ivu-modal-body) {
  padding: 0;
}

:deep(.ivu-modal-content) {
  border-radius: 4px;
}

.content {
  display: flex;
  align-items: center;

  &.horizontal{
    flex-direction: column;
    gap:10px;
  }

  .icon-warnning {
    width: 44px;
    color: var(--primary-color);
    font-size: 44px;
  }

  .description {
    margin-left: 15px;
    color: fade(#000, 85%);
    font-weight: normal;
    font-size: 16px;
  }

  .action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    .button-primary {
      width: 90px;
      line-height: 32px;
      border: none;
    }
  }
}
</style>
