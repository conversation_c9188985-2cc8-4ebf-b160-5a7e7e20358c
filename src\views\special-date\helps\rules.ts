import { namespaceT } from '@/helps/namespace-t';
import { createInputRules, createSelectRules } from '@/helps/rules';


const timeRules = (rule, value, callback) => {
  const tc = namespaceT('common.error');
  if (Array.isArray(value) && value.length > 0) {
    value.forEach((item) => {
      if (!(item.startDate && item.endDate)) {
        callback(new Error(tc('thisFieldIsRequired')));
      }
    });
    callback();
  } else {
    callback(new Error(tc('thisFieldMustBeSelected')));
  }
};

export const createModalFormRules = () => {
  return {
    type: [createSelectRules()],
    name: [createInputRules()],
    time: [
      {
        required: true,
        validator: timeRules,
        trigger: 'change',
      },
    ],
    isEnable: [createSelectRules()],
  };
};
