<script setup lang="ts">
import { computed } from 'vue';
import { getLeaveUnitOptions } from '@/utils/special-date';

const options = computed(() => getLeaveUnitOptions());

</script>

<template>
  <RadioGroup
    class="pima-radio-group-vertical"
    v-bind="$attrs"
  >
    <Radio
      v-for="item in options"
      :key="item.value"
      :label="item.value"
    >
      {{ item.label }}
    </Radio>
  </RadioGroup>
</template>
