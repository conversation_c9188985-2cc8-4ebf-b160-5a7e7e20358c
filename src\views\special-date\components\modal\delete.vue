<script setup lang="ts">
import ModalWarning from '@/components/common/modal-warning.vue';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-confirm':[]
}>();
const t = namespaceT('specialDate');
const tm = namespaceT('common');

const loading = defineModel<boolean>('loading');
const visible = defineModel<boolean>('visible');

const onCancel = () => {
  visible.value = false;
};

</script>


<template>
  <ModalWarning
    :visible="visible"
    :description="t('content.delete')"
    custom-footer
    horizontal
    @on-cancel="onCancel"
  >
    <template #button>
      <div class="action">
        <Button
          type="primary"
          class="button-primary pima-btn"
          :loading="loading"
          @click="emit('on-confirm')"
        >
          {{ tm('action.confirm') }}
        </Button>
      </div>
    </template>
  </ModalWarning>
</template>


<style lang="less" scoped>
.action{
  display: flex;
  justify-content: center;
}
</style>
