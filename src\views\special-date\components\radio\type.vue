<script setup lang="ts">
import { computed } from 'vue';

import { getSpecialDateTypeOptions } from '@/utils/special-date';

const options = computed(() => getSpecialDateTypeOptions());

</script>

<template>
  <RadioGroup
    class="pima-radio-group-vertical"
    v-bind="$attrs"
  >
    <Radio
      v-for="item in options"
      :key="item.value"
      :label="item.value"
    >
      {{ item.label }}
    </Radio>
  </RadioGroup>
</template>
