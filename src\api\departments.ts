import { LOCAL_BDC_ARCH_API_BASE_URL } from '@/config/api';
import { CommonApi } from '@/api/common/common-api';


export class DepartmentsApi extends CommonApi {
  constructor() {
    super({
      baseURL: LOCAL_BDC_ARCH_API_BASE_URL,
    });
  }

  url() {
    return '/depts';
  }

  async send() {
    const { model } = await super.send();

    if (!model) {
      return [];
    }

    return model;
  }
}
