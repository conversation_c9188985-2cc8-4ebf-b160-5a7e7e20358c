<script lang='ts' setup>
import { defineAsyncComponent, ref, onMounted } from 'vue';

import { PUBLIC_PATH } from '@/config/public-path';
import emitter from '@/utils/event-bus';


// @ts-expect-error: Cannot find module 'pimaRemoteUI/PimaAppHeader' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaAppHeader = defineAsyncComponent(() => import('pimaRemoteUI/PimaAppHeader'));

const show = ref(false);

onMounted(() => {
  show.value = true;
});

function onClickServiceName() {
  emitter.emit('clickServiceName');
}

function onLogout() {
  const redirect = encodeURIComponent(window.location.href);
  const url = `${PUBLIC_PATH}logout?service=${redirect}`;
  window.location.replace(url);
}

function onUserDataFetched(data) {
  emitter.emit('userDataFetched', data);
}
</script>


<template>
  <PimaAppHeader
    v-if="show"
    @click-service-name="onClickServiceName()"
    @logout="onLogout()"
    @user-data-fetched="onUserDataFetched"
  />
</template>
