<!-- eslint-disable import/order -->
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import _ from 'lodash';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import Search from '../components/search.vue';
import QueryTable from '../components/query-table.vue';
import ModalBatchCopy from '../components/modal/batch-copy.vue';

import { TableListApi } from '@/api/leave-type/list';
import { EnableStatus } from '@/consts/leave-type';
import { openToastError } from '@/helps/toast';
import { useQueryTable } from '@/uses/query-table';

import { createSimpleSearchModel, TableRowData } from '../helps/data';


const modalBatchCopyRef = ref(null);
const selectedRowsId = ref([]);

async function loadData(option) {
  try {
    const params = option || {};

    const api = new TableListApi({});
    api.params = params;
    const res = await api.send();
    const cloneData = _.cloneDeep(res);
    cloneData.data = cloneData.data.map((item) => {
      return {
        ...item,
        isEnable: item.isEnable ? EnableStatus.ENABLE : EnableStatus.DISABLE,
      };
    });
    return cloneData;
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
}

const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSimpleSearchModel(),
});


const onSelectChange = (selection:TableRowData[]) => {
  selectedRowsId.value = selection.map((item) => item.id);
};
const onModalBatchCopyOpen = () => {
  modalBatchCopyRef.value.Open();
};

onMounted(() => {
  qt.load();
});

</script>


<template>
  <Search
    v-model:selected-rows-id="selectedRowsId"
    v-model="qt"
    @on-search="qt.search"
    @on-batch-copy="onModalBatchCopyOpen"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      :page="qt.page"
      @on-reload="qt.load"
      @on-selection-change="onSelectChange"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>

  <ModalBatchCopy
    ref="modalBatchCopyRef"
    :ids="selectedRowsId"
    @on-success-copy="qt.load"
  />
</template>
