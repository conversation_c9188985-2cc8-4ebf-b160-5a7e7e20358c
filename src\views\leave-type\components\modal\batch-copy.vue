<script setup lang="ts">
import { ref } from 'vue';

import ModalForm from '@/components/common/modal-form.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';

import { CopyApi } from '@/api/leave-type/copy';
import { CommonDepartmentsParams } from '@/consts/common-departments';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';


const props = withDefaults(defineProps<{
  ids:number[]
}>(), {
  ids: null,
});

const emit = defineEmits<{
  'on-success-copy':[],
}>();


const t = namespaceT('leaveType');
const tc = namespaceT('common');
const loading = ref(false);
const visible = ref(false);
const model = ref({
  deptIds: [],
});


const changeLoadingStatus = () => {
  loading.value = !loading.value;
};


const onCopy = async () => {
  try {
    changeLoadingStatus();
    const params = {
      deptIds: [...model.value.deptIds.map((item) => item.id)],
      leaveTypeIdList: [props.ids],

    };

    const api = new CopyApi({});
    api.params = params;
    await api.send();
    openToastSuccess(t('hint.successCopy'));
    emit('on-success-copy');
  } catch (error) {
    openToastError(error.message);
  } finally {
    changeLoadingStatus();
  }
};


const onCancel = () => {
  visible.value = false;
  model.value.deptIds = [];
};


const onValidate = () => {
  if (model.value.deptIds.length === 0) {
    openToastError(t('hint.mustBeSelectDept'));
  } else {
    onCopy();
    onCancel();
  }
};


defineExpose({
  Close: () => { visible.value = false; },
  Open: () => { visible.value = true; },
});

</script>

<template>
  <ModalForm
    v-model="visible"
    :width="700"
    :title="t('actions.batchCopy')"
    @on-cancel="onCancel"
  >
    <template #content>
      <Form
        ref="formRef"
        :model="model"
        hide-required-mark
      >
        <FormItem
          :label="t('content.batchCopy',{count:ids.length||0})"
          prop="deptIds"
        >
          <SelectPimaRemoteDepartment
            v-model="model.deptIds"
            multiple
            transfer
            is-concat-parent-dept-name
            :parent-code="CommonDepartmentsParams.Student"
            :width="350"
          />
        </FormItem>
      </Form>
    </template>
    <template #button>
      <Button
        class="pima-btn"
        :disabled="loading"
        @click="onCancel"
      >
        {{ tc('action.cancel') }}
      </Button>

      <Button
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click="onValidate"
      >
        {{ tc('action.save') }}
      </Button>
    </template>
  </ModalForm>
</template>
