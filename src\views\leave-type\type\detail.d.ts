/**
 * LeaveTypeVO，请假类型
 */
export interface LeaveTypeVO {
  /**
   * 支持附件-多个支持后缀使用英文','隔开
   */
  attExt?: string;
  /**
   * 附件大小
   */
  attSize?: number;
  /**
   * 休假说明
   */
  content?: string;
  /**
   * 适用部门数据数组
   */
  deptDataList?: LeaveTypeDeptDataVO[];
  /**
   * 适用部门ID
   */
  deptIds?: number[];
  /**
   * 适用部门名称
   */
  deptNames?: string[];
  /**
   * id
   */
  id?: number;
  /**
   * 是否启用
   */
  isEnable?: boolean;
  /**
   * 是否限制附件大小 枚举[需要：true,不需要：false]
   */
  isLimitAttSize?: boolean;
  /**
   * 是否限制累计请假时长 枚举[是：true,否：false]
   */
  isLimitLeaveAmt?: boolean;
  /**
   * 是否限制请假时间 枚举[是：true,否：false]
   */
  isLimitLeaveTime?: boolean;
  /**
   * 是否上传附件 枚举[需要：true,不需要：false]
   */
  isNeedAtt?: boolean;
  /**
   * 可请假范围结束时间 yyyy-MM-dd
   */
  leaveEndTime?: Date;
  /**
   * 可请假范围开始时间 yyyy-MM-dd
   */
  leaveStartTime?: Date;
  /**
   * 累计请假时长
   */
  maxLeaveAmtPerPeriod?: number;
  /**
   * 单次最大假期时长
   */
  maxLeaveLength?: number;
  /**
   * 请假名称
   */
  name?: string;
  /**
   * 适用范围 - all（全部）/dept（部门）
   */
  scope?: string;
  /**
   * 适用人员集——内容传输格式为-
   * {
   * {userId: xx, userName: xx},
   * ...
   * }
   */
  users?: string;
  [property: string]: any;
}

/**
* LeaveTypeDeptDataVO，请假类型部门VO
*/
export interface LeaveTypeDeptDataVO {
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  [property: string]: any;
}
