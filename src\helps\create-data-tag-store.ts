import { defineStore } from 'pinia';
import _ from 'lodash';

import { DataTagApi } from '@/api/data-tag';
import { DataTags } from '@/consts/data-tags';


export function createDataTagStore(storeName: string, getTypeCode: (codes: typeof DataTags) => DataTags) {
  const initState = {
    data: null,
    options: [],
    loaded: false,
    loading: false,
  };

  const getters = {
    getTextByCode(state) {
      return function getTextByCode(code) {
        const match = _.find(state.data || [], { code });
        return match ? match.nameByLocale : null;
      };
    },
    getOptions(state) {
      if (state.data && state.data.length > 0) {
        return state.data.map((item) => {
          return {
            value: item.code,
            label: item.name,
          };
        });
      }
      return [];
    },
  };

  const actions = {
    async loadDataIfNeeded() {
      if (this.loading || this.loaded) {
        return;
      }

      this.loading = true;
      try {
        const typeCode = getTypeCode(DataTags);
        const api = new DataTagApi({ typeCode });
        const data = await api.send();
        this.data = data;
        this.loaded = true;
      } catch (error) {
        this.data = null;
        throw error;
      } finally {
        this.loading = false;
      }
    },
  };

  return defineStore(storeName, {
    state: () => initState,
    actions,
    getters,
  });
}
