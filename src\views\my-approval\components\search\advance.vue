<script setup lang="ts">
import WrapperSearchAdvanced from '@/components/common/wrapper-search-advanced.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import PimaInput from '@/components/common/pima-input.vue';
import PickerDayRange from '@/components/common/picker/picker-day-range.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';
import SelectApprovalStatus from '@/components/biz/select/approval-status.vue';
import SelectLeaveType from '@/components/biz/select/leave-type.vue';
import SelectApprovalType from '@/components/biz/select/approval-type.vue';

import { CommonDepartmentsParams } from '@/consts/common-departments';
import { MaxLength } from '@/consts/max-length';
import { namespaceT } from '@/helps/namespace-t';
import { UseQueryTableReturn } from '@/uses/query-table';


const qt = defineModel<UseQueryTableReturn>();

const t = namespaceT('myApproval');
const tc = namespaceT('common');


</script>


<template>
  <WrapperSearchAdvanced
    :form-search="qt"
  >
    <Row>
      <!--  学院 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.college')">
          <SelectPimaRemoteDepartment
            v-model="qt.advancedSearchModel.deptId"
            clearable
            :parent-code="CommonDepartmentsParams.Student"
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <!-- 请假类型 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.leaveType')">
          <SelectLeaveType
            v-model="qt.advancedSearchModel.leaveTypeId"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <!--  状态 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.approvalStatus')">
          <SelectApprovalStatus
            v-model="qt.advancedSearchModel.status"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>

      <!-- 申请单号 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.applyOrder')">
          <PimaInput
            v-model.trim="qt.advancedSearchModel.sn"
            clearable
            :placeholder="tc('placeholder.input')"
            :max-length="MaxLength.INPUT_MAX_LENGTH"
          />
        </PairLabelItem>
      </Col>

      <!-- 申请人 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.applier')">
          <PimaInput
            v-model.trim="qt.advancedSearchModel.applyUserName"
            clearable
            :placeholder="t('search.placeholder.applier')"
            :max-length="MaxLength.INPUT_MAX_LENGTH"
          />
        </PairLabelItem>
      </Col>

      <!-- 申请时间 -->
      <Col
        :xl="16"
        :xxl="12"
      >
        <PairLabelItem :label="t('search.label.applyTime')">
          <PickerDayRange
            v-model:min="qt.advancedSearchModel.startDate"
            v-model:max="qt.advancedSearchModel.endDate"
          />
        </PairLabelItem>
      </Col>

      <!-- 审批状态 -->
      <Col
        :xl="8"
        :xxl="6"
      >
        <PairLabelItem :label="t('search.label.approvalType')">
          <SelectApprovalType
            v-model="qt.advancedSearchModel.applyType"
            clearable
            :placeholder="tc('placeholder.all')"
          />
        </PairLabelItem>
      </Col>
    </Row>
  </WrapperSearchAdvanced>
</template>
