<template>
  <Paginator
    v-if="qt.table.total > 0"
    v-model:page-index="qt.page.page"
    v-model:page-size="qt.page.limit"
    :total="qt.table.total"
    :simple="simple"
    @change="qt.turnPage"
  />
</template>


<script lang='ts'>
import { defineComponent, onBeforeUnmount, reactive, toRef, watch } from 'vue';

import Paginator from '@/components/common/paginator.vue';


export default defineComponent({
  name: 'PaginatorQt',

  components: {
    Paginator,
  },

  props: {
    queryTable: {
      type: Object,
      required: true,
    },
    simple: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },

  setup(props) {
    const queryTable = toRef(props, 'queryTable');

    const qt = reactive(queryTable);

    const watchStop = watch(queryTable, (val) => {
      Object.assign(qt, val);
    }, {
      deep: true,
    });

    onBeforeUnmount(() => {
      watchStop();
    });

    return {
      qt,
    };
  },
});
</script>
