import { RouterName as RN } from './router';


// 侧边栏菜单编码，键值对一对一，需包含子级菜单
// 不包含子级菜单关系，子级菜单关系由 SiderMenuItemsFlatMap 维护
// 存储所有的菜单编码
export enum SiderMenuCodes {
  MyApproval = 'MY_APPROVAL',
  LeaveQuery = 'LEAVE_QUERY',
  LeaveType = 'LEAVE_TYPE',
  SpecialDate = 'SPECIAL_DATE',
}


const SMC = SiderMenuCodes;

// 侧边栏菜单对照
// 存储所有一/二级菜单(编码)到路由配置的映射
// 子级节点由 children 控制，最多包含一级子菜单，全部总两级，超过两级会自动忽略
export const SiderMenuItemsFlatMap = new Map([
  [SMC.MyApproval, { routeName: RN.MyApproval }],
  [SMC.LeaveQuery, { routeName: RN.LeaveQuery }],
  [SMC.LeaveType, { routeName: RN.LeaveType }],
  [SMC.SpecialDate, { routeName: RN.SpecialDate }],


]);
