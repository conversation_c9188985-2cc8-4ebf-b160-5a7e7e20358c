import { reactive, ref, toRefs } from 'vue';
import _ from 'lodash';
import { SiderMenuCodes, SiderMenuItemsFlatMap } from '@/config/sider-menu';
import { forestNodes } from '@/utils/tree';


export interface GetMenuName {
  (fn: (codes: typeof SiderMenuCodes) => SiderMenuCodes): string;
}

function Sider() {
  const state = reactive({
    menu: [],
    menuMap: new Map(),
  });
  const isShow = ref(false);


  function shutSider() {
    isShow.value = false;
  }

  function openSider() {
    isShow.value = true;
  }

  function setMenu(menu) {
    const menuMap = new Map();
    // eslint-disable-next-line no-restricted-syntax
    for (const item of forestNodes(menu)) {
      menuMap.set(item.key, item);
    }

    state.menu = menu;
    state.menuMap = menuMap;
  }

  function setMenuItemBadge(items = []) {
    if (!Array.isArray(items)) {
      return;
    }

    items.forEach(({ key, badge }) => {
      state.menu.forEach((item) => {
        if (item.key === key) {
          Object.assign(item, {
            badge,
          });
        }
      });
    });
  }

  function getRouterConfig({ key }) {
    return SiderMenuItemsFlatMap.get(key);
  }

  function getFirstRoute() {
    const firstMenuItem = _.head(state.menu);
    const firstMenuSubitem = _.head(_.get(firstMenuItem, 'children', []));
    const route = getRouterConfig(firstMenuSubitem || firstMenuItem);
    return route;
  }

  function getMenuName(fn) {
    const m = fn(SiderMenuCodes);
    return state.menuMap.get(m).title || '';
  }


  return {
    ...toRefs(state),
    isShow,

    shutSider,
    openSider,
    setMenu,
    setMenuItemBadge,
    getRouterConfig,
    getFirstRoute,
    getMenuName,
  };
}

class SiderManager {
  static getInstance() {
    if (!this.sider) {
      this.sider = new Sider();
    }

    return this.sider;
  }
}

export function useSider() {
  return SiderManager.getInstance();
}
