// eslint-disable-next-line import/order
import { CommonApi } from '../../common/common-api';

import { namespaceT } from '@/helps/namespace-t';
import { BaseError } from '@/errors/base-error';

enum ErrorCode {
  ALREADY_EXISTS = 'ALREADY_EXISTS',
}

export class CheckCannotDeleteApi extends CommonApi {
  id:number;

  constructor(args) {
    super({});
    this.id = args.id;
  }

  url() {
    return `/leave-types/${this.id}/sub-check`;
  }

  async send() {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('errors.leaveType');
      switch (error.code) {
        case ErrorCode.ALREADY_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
