
<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

// @ts-expect-error: Cannot find module 'pimaRemoteUI/PimaAppHeader' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaSelectDepartment = defineAsyncComponent(() => import('pimaRemoteUI/PimaSelectDepartment'));


withDefaults(
  defineProps<{
    width?:number
    transfer?:boolean
    multiple?:boolean
  }>(),
  {
    width: 0,
    transfer: false,
    multiple: false,
  },

);

const emit = defineEmits<{
  'on-change':[]
}>();


</script>

<template>
  <PimaSelectDepartment
    v-bind="$attrs"
    class="select-department"
    :transfer="transfer"
    :multiple="multiple"
    :style="{'--inject-width': (width? `${width}px`: '100%')}"
    :class="{'single-select':!multiple}"
    @on-select-changed="emit('on-change')"
  />
</template>

<style lang="less">
.pima-select-people-tree-filter{
  max-height: 300px;
}
</style>

<style lang="less" scoped>

.pima-select-department.select-department {
  display: inline-block;
  width: var(--inject-width);

  :deep(.pima-select-input-people-search) {
    max-height: 200px;
    overflow: hidden;
    background-color: #fff;
  }

  &.single-select {
    :deep(.pima-select-input-people-search){
        display: flex;
        align-items: center;
    }
  }
}

</style>
