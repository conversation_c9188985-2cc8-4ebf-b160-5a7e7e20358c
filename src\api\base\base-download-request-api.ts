import { saveAs } from 'file-saver';
import { BaseRequestApi } from './base-request-api';


export class BaseDownloadRequestApi extends BaseRequestApi {
  constructor(args) {
    super({
      ...args,
      responseType: 'blob',
    });
  }

  async send() {
    const response = await super.send();
    const { headers, data } = response;
    if (data instanceof Blob) {
      const contentDisposition = headers['content-disposition'];
      let fileName = contentDisposition ? decodeURIComponent(contentDisposition.match(/filename=(.+)/)[1]) : 'untitled';
      fileName = fileName.replace(/"/g, '');
      saveAs(data, fileName);
    }
  }
}
