/**
 * ModalTypeVO 类型详情VO
 */
export interface ModalTypeVO {

  typeName?:string;
  /**
   * 适用部门数据数组
   */
  deptDataList?: LeaveTypeDeptDataVO[];
  /**
   * 适用部门ID
   */
  deptIds?: number[];
  /**
   * 适用部门名称
   */
  deptNames?: string[];
  /**
   * id
   */
  id?: number;
  /**
   * 是否启用
   */
  isEnable?: boolean;
  /**
   * 是否限制累计请假时长 枚举[是：true,否：false]
   */
  isLimitLeaveAmt?: boolean;
  /**
   * 可请假范围结束时间 yyyy-MM-dd
   */
  leaveEndTime?: Date;
  /**
   * 可请假范围开始时间 yyyy-MM-dd
   */
  leaveStartTime?: Date;
  /**
   * 累计请假时长
   */
  maxLeaveAmtPerPeriod?: number;
  /**
   * 单次最大假期时长
   */
  maxLeaveLength?: number;

  [property: string]: any;
}

/**
* LeaveTypeDeptDataVO，请假类型部门VO
*/
export interface LeaveTypeDeptDataVO {
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  [property: string]: any;
}
