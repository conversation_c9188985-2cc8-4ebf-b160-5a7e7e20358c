<script setup lang="ts">
import { ref } from 'vue';

import WrapperFormBlock from '@/components/common/wrapper-form-block.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
// eslint-disable-next-line import/order
import Other from '../form-detail/other.vue';

import { namespaceT } from '@/helps/namespace-t';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';

import { LeaveConfigModelType, LeaveConfigType } from '../../helps/data';


const emit = defineEmits<{
  'on-add':[],
  'on-remove': [LeaveConfigModelType],
}>();

const t = namespaceT('leaveType');
const model = defineModel<LeaveConfigType>();
const formRef = ref();

const validate = (cb: (isValid: boolean) => void) => {
  formRef.value.validate(async (valid) => {
    if (!valid) {
      formScrollIntoError(formRef.value);
    }
    cb(valid);
  });
};


defineExpose({
  validate,
});

</script>

<template>
  <Form
    ref="formRef"
    :model="model"
    :label-width="150"
    class="form-detail"
  >
    <PairLabelItem
      v-for="(item,index) in model.list"
      :key="model.list[index].uuid"
      :no-colon="false"
      :label="t('label.leave', { index: index+1 })"
    >
      <WrapperFormBlock class="item-block">
        <Other
          v-model="model.list[index]"
          :index="index"
        />
      </WrapperFormBlock>

      <div class="action-wrap">
        <Icon
          type="ios-add-circle-outline"
          color="#1f65e0"
          class="icon mr-5"
          :size="20"
          @click="emit('on-add')"
        />

        <Icon
          v-if="(model.list.length > 1)"
          type="ios-remove-circle-outline"
          color="#d63c3c"
          class="icon"
          :size="20"
          @click="emit('on-remove', model.list[index])"
        />
      </div>
    </PairLabelItem>
  </Form>
</template>

<style lang="less" scoped>
.form-detail {
  padding-bottom: 60px;
}

.action-wrap {
  display: inline-block;
  width: 60px;
  padding-top: 20px;

  .icon {
    cursor: pointer;
  }
}

:deep(.pima-pair-label-item){
  align-items: flex-start;

  &:first-child{
    margin-left: 15px;
  }

  &:not(:last-child){
    margin-bottom: 10px;
    border-bottom: 1px solid #efefef;
  }

  .label{
    padding-top: 20px;
  }

  .content{
    display: flex;
  }
}
</style>
