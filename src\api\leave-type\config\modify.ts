// eslint-disable-next-line import/order
import { CommonApi } from '../../common/common-api';

import { namespaceT } from '@/helps/namespace-t';
import { BaseError } from '@/errors/base-error';


enum ErrorCode {
  NO_EXISTS = 'NO_EXISTS',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
}

export class ModifyApi extends CommonApi {
  id:number;

  constructor(args) {
    super({});
    this.id = args.id;
  }

  url() {
    return `/leave-types/${this.id}/sub`;
  }

  method(): string {
    return 'POST';
  }

  async send() {
    try {
      await super.send();
    } catch (error) {
      const t = namespaceT('errors.leaveType');

      switch (error.code) {
        case ErrorCode.NO_EXISTS:
        case ErrorCode.ALREADY_EXISTS:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
