<script setup lang="ts">
import SearchForSimple from './search/simple.vue';
import SearchForAdvance from './search/advance.vue';

// eslint-disable-next-line import/order
import { UseQueryTableReturn } from '@/uses/query-table';


const qt = defineModel<UseQueryTableReturn>();

</script>


<template>
  <!-- 简单搜索 -->
  <SearchForSimple
    v-if="qt.shown.value"
    v-model="qt"
    @on-search="qt.search"
  />

  <!-- 高级搜索 -->
  <SearchForAdvance
    v-else
    v-model="qt"
  />
</template>
