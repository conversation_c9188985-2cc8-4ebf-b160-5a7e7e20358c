/**
 * LeaveTypeVO，请假类型
 */
export interface LeaveTypeVO {
  /**
   * 支持附件-多个支持后缀使用英文','隔开
   */
  attExt?: string;
  /**
   * 附件大小
   */
  attSize?: number;
  /**
   * 请假时长计算方式 - work-day（工作日）/nature-day（自然日）
   */
  calMethod?: string;
  /**
   * 代码
   */
  code?: string;
  /**
   * 休假说明
   */
  content?: string;
  /**
   * 适用部门数据数组
   */
  deptDataList?: LeaveTypeDeptDataVO[];
  /**
   * 适用部门ID
   */
  deptIds?: number[];
  /**
   * 适用部门名称
   */
  deptNames?: string[];
  /**
   * 其他规则json
   */
  features?: string;
  /**
   * id
   */
  id: number;
  /**
   * 是否需要余额配置
   */
  isBalanceConfig?: boolean;
  /**
   * 是否启用
   */
  isEnable?: boolean;
  /**
   * 是否限制附件大小
   */
  isLimitAttSize?: boolean;
  /**
   * 是否只能在假期有效期内申请
   */
  isLimitPeriodUse?: boolean;
  /**
   * 是否上传附件
   */
  isNeedAtt?: boolean;
  /**
   * 是否提前申请
   */
  isNeedLeaveEarly?: number;
  /**
   * 是否带薪
   */
  isWithSalary?: boolean;
  /**
   * 提前申请的天数
   */
  leaveEarlyDays?: number;
  /**
   * 时长单位
   */
  leaveUnit?: string;
  /**
   * 周期内最大请假时长
   */
  maxLeaveAmtPerPeriod?: number;
  /**
   * 最大请假时长
   */
  maxLeaveLength?: number;
  /**
   * 最小请假单位- half-day（半天）/day（天）/hour（小时）
   */
  minLeaveUnit?: string;
  /**
   * 类型名称
   */
  name?: string;
  /**
   * 适用范围 - all（全部）/dept（部门）
   */
  scope?: string;
  /**
   * 适用人员集——内容传输格式为-
   * {
   * {userId: xx, userName: xx},
   * ...
   * }
   */
  // 假期名称
  leaveSubTypes?:LeaveSubTypeVO[];


  users?: string;
  [property: string]: any;
}

/**
* LeaveTypeDeptDataVO，请假类型部门VO
*/
export interface LeaveTypeDeptDataVO {
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  [property: string]: any;
}


/**
 * LeaveSubTypeVO，请假子类型
 */
export interface LeaveSubTypeVO {
  /**
   * 支持附件-多个支持后缀使用英文','隔开
   */
  attExt?: string;
  /**
   * 附件大小
   */
  attSize?: number;
  /**
   * 编码
   */
  code?: string;
  /**
   * 休假说明
   */
  content?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 是否启用
   */
  isEnable?: boolean;
  /**
   * 是否限制附件大小 枚举[需要：true,不需要：false]
   */
  isLimitAttSize?: boolean;
  /**
   * 是否上传附件 枚举[需要：true,不需要：false]
   */
  isNeedAtt?: boolean;
  /**
   * 类型名称
   */
  name?: string;
  [property: string]: any;
}
