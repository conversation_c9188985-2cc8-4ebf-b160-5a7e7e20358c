<script setup lang="ts">
import PimaImportExportCenter from '@/components/common/import-export-center.vue';
import SearchForSimple from './search/simple.vue';
import SearchForAdvance from './search/advance.vue';

// eslint-disable-next-line import/order
import { UseQueryTableReturn } from '@/uses/query-table';

withDefaults(defineProps<{
  selectRows:number[],
}>(), {
  selectRows: null,
});

const qt = defineModel<UseQueryTableReturn>();


</script>


<template>
  <!-- 简单搜索 -->
  <SearchForSimple
    v-model="qt"
    :select-rows="selectRows"
    @on-search="qt.search"
  />

  <!-- 高级搜索 -->
  <SearchForAdvance
    v-if="!qt.shown.value"
    v-model="qt"
  />

  <PimaImportExportCenter hide-import-task />
</template>
