import { AttachmentFormate, EnableStatus, IsNeedAttachment } from '@/consts/leave-type';
import { namespaceT } from '@/helps/namespace-t';
import { EmptyText } from '@/consts/empty-text';

export const getEnableStatusText = (status: EnableStatus) => {
  const t = namespaceT('leaveType.status');
  const map = new Map(
    Object.values(EnableStatus).map((item) => [item, t(item)]),
  );
  if (map.has(status)) {
    return map.get(status);
  }

  return EmptyText;
};


// 是否启用
export const getEnableStatusOptions = () => {
  const list = Object.values(EnableStatus).map((item) => {
    return {
      label: getEnableStatusText(item),
      value: item,
    };
  });

  return list;
};


// 附件格式
export const getFileFormatOptions = () => {
  const list = Object.values(AttachmentFormate).map((item) => {
    return {
      label: item,
      value: `.${item}`,
    };
  });

  return list;
};

// 附件要求
export const getIsNeedAttachmentOptions = () => {
  const t = namespaceT('leaveType.isNeedAttachment');
  const list = Object.values(IsNeedAttachment).map((item) => ({
    label: t(item),
    value: item,
  }));

  return list;
};
