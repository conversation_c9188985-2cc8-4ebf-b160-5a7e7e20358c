<script setup lang="ts">
import { ref } from 'vue';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';
// eslint-disable-next-line import/order
import ModalTypeAdd from '../modal/add-type.vue';

import { CommonDepartmentsParams } from '@/consts/common-departments';
import { MaxLength } from '@/consts/max-length';
import { namespaceT } from '@/helps/namespace-t';
import { UseQueryTableReturn } from '@/uses/query-table';

const emit = defineEmits<{
  'on-search': [],
  'on-batch-copy':[]
}>();

const selectedRowsId = defineModel<number[]>('selectedRowsId');
const qt = defineModel<UseQueryTableReturn>();
const modalTypeRef = ref(null);

const t = namespaceT('leaveType');
const tc = namespaceT('common');
const onOpenModalType = () => {
  modalTypeRef.value.onOpen();
};

</script>

<template>
  <WrapperSearchSimple
    :show="qt.shown.value"
  >
    <!--  学院 -->
    <PairLabelItem :label="t('search.label.college')">
      <SelectPimaRemoteDepartment
        v-model="qt.simpleSearchModel.deptId"
        :width="200"
        :placeholder="tc('placeholder.all')"
        :parent-code="CommonDepartmentsParams.Student"
        clearable
        @on-change="emit('on-search')"
      />
    </PairLabelItem>

    <!-- 搜索框 -->
    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="qt.simpleSearchModel.keyword"
        clearable
        class="w-400"
        :placeholder="t('search.placeholder.keyword')"
        :max-length="MaxLength.INPUT_MAX_LENGTH"
        @on-clear="emit('on-search')"
        @on-search="emit('on-search')"
      />
    </PairLabelItem>

    <template #right>
      <div class="action-wrap">
        <Button
          v-if="$can(P=>P.LeaveType.BatchCopy)"
          class="pima-btn"
          type="primary"
          :disabled="!selectedRowsId?.length"
          @click="emit('on-batch-copy')"
        >
          {{ t('actions.batchCopy') }}
        </Button>

        <Button
          v-if="$can(P=>P.LeaveType.Add)"
          class="pima-btn"
          type="primary"
          @click="onOpenModalType"
        >
          {{ t('actions.addType') }}
        </Button>
      </div>
    </template>
  </WrapperSearchSimple>

  <ModalTypeAdd
    ref="modalTypeRef"
    @save-success="qt.load"
  />
</template>

<style lang="less" scoped>
.action-wrap{
  .pima-btn{
    &:not(:last-child){
      margin-right: 20px;
    }
  }
}
</style>
