import { ApprovalType } from '@/consts/approval-type';
import { namespaceT } from '@/helps/namespace-t';
import { getEmptyText } from './empty-text';

// 审批类型
export const getApprovalTypeText = (type: ApprovalType) => {
  const t = namespaceT('consts.approvalType');
  const map = new Map(Object.values(ApprovalType).map((item) => [item, t(item)]));

  if (map.has(type)) {
    return map.get(type);
  }

  return getEmptyText();
};

export const getApprovalTypeOptions = () => {
  return Object.values(ApprovalType).map((item) => {
    return {
      label: getApprovalTypeText(item),
      value: item,
    };
  });
};
