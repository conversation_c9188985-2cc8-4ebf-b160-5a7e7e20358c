
import { v4 as uuidV4 } from 'uuid';
import { AttachmentFormate, EnableStatus, IsNeedAttachment } from '@/consts/leave-type';
import { LeaveTypeVO } from '../type/list';

export type TableRowData = LeaveTypeVO;

export interface SearchModel {
  deptId:number,
  keyword:string
}

interface DeptIdsType {
  id:number
  [property: string]: any;
}

export const createSimpleSearchModel = ():SearchModel => {
  return {
    deptId: null,
    keyword: null,
  };
};

export interface FormModel {
  name:string,
  code?:string,
  deptIds:number[] | DeptIdsType[],

  leaveTime:{
    leaveEndTime:string,
    leaveStartTime:string,
  }
  maxLeaveLength:number,
  maxLeaveAmtPerPeriods:{
    days:number,
    limit:boolean
  }

  isNeedAtt:IsNeedAttachment,
  attExt: AttachmentFormate[],
  attSize:number,

  content:string,

  isEnable:EnableStatus
}

export const createFormModel = ():FormModel => {
  return {
    name: null,
    code: null,
    deptIds: [],

    leaveTime: {
      leaveEndTime: null,
      leaveStartTime: null,
    },
    maxLeaveLength: null,
    maxLeaveAmtPerPeriods: {
      days: null,
      limit: false,
    },

    isNeedAtt: IsNeedAttachment.NO,
    attExt: [],
    attSize: null,
    content: null,
    isEnable: EnableStatus.ENABLE,

  };
};


export interface AddOrModifyTypeModalType {
  // 假期名称
  name:string,
  deptIds:number[] | DeptIdsType[],

  leaveTime:{
    leaveEndTime:string,
    leaveStartTime:string,
    notLimit:boolean
  }

  maxLeaveLength:number,

  maxLeaveAmtPerPeriods:{
    days:number,
    limit:boolean
  }

  isEnable:EnableStatus
}


export const createTypeModel = ():AddOrModifyTypeModalType => {
  return {
    name: null,
    deptIds: [],

    leaveTime: {
      leaveEndTime: null,
      leaveStartTime: null,
      notLimit: false,
    },
    maxLeaveLength: null,
    maxLeaveAmtPerPeriods: {
      days: null,
      limit: false,
    },

    isEnable: EnableStatus.ENABLE,

  };
};

export interface LeaveConfigType {
  list:LeaveConfigModelType[],
}

export interface LeaveConfigModelType {
  id?:number;
  uuid:number,
  name:string,
  code?:string,

  isNeedAtt:IsNeedAttachment,
  attExt: AttachmentFormate[] | string,
  attSize:number,

  content:string,

  isEnable:EnableStatus
}

export const createLeaveConfigItem = ():LeaveConfigModelType => {
  return {
    uuid: uuidV4(),
    name: null,
    code: null,

    isNeedAtt: IsNeedAttachment.NO,
    attExt: [],
    attSize: null,
    content: null,
    isEnable: EnableStatus.ENABLE,

  };
};


export const createLeaveConfigModel = ():LeaveConfigType => {
  return {
    list: [createLeaveConfigItem()],
  };
};
