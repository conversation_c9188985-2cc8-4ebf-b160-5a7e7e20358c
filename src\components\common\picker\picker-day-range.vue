<script setup lang="ts">
import { isValid } from 'date-fns';
import { computed } from 'vue';

import { useDateRange } from '@/uses/date-range';
import PickerFormatDate from './format-date.vue';


enum ValidTypes {
  Date = 'date',
  Daterange = 'daterange',
  Datetime = 'datetime',
  Datetimerange = 'datetimerange',
  Year = 'year',
  Month = 'month',
}

defineOptions({
  name: 'PickerDayRange',
});

interface Props {
  disabled?: boolean;
  type?: ValidTypes;
  format?: string;
  min?: Date | unknown;
  max?: Date | unknown;
  limitMin?: Date | unknown;
  limitMax?: Date | unknown;
  transfer?: boolean;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  showSeparator?:boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  type: ValidTypes.Date,
  format: 'yyyy/MM/dd',
  min: null,
  max: null,
  limitMin: null,
  limitMax: null,
  transfer: false,
  minPlaceholder: '',
  maxPlaceholder: '',
  showSeparator: true,
});

const emit = defineEmits([
  'update:min',
  'on-change-min',
  'update:max',
  'on-change-max',
]);

const minValue = computed({
  get() {
    return props.min;
  },
  set(val) {
    emit('update:min', val);
    emit('on-change-min', val);
  },
});

const maxValue = computed({
  get() {
    return props.max;
  },
  set(val) {
    emit('update:max', val);
    emit('on-change-max', val);
  },
});

const validType = computed(() => {
  if (!Object.keys(ValidTypes).includes(props.type)) {
    return ValidTypes.Date;
  }
  return props.type;
});

function getDateByType(date) {
  if (!isValid(date) || validType.value === ValidTypes.Date) {
    return date;
  }

  const y = date.getFullYear();
  const m = date.getMonth();
  return new Date(y, validType.value === ValidTypes.Month ? m : 0, 1);
}

const dateRange = useDateRange(
  {
    min: () => getDateByType(minValue.value),
    max: () => getDateByType(maxValue.value),
  },
  {
    min: () => getDateByType(props.limitMin),
    max: () => getDateByType(props.limitMax),
  },
);
</script>


<template>
  <div
    class="day-range-picker"
    :class="{'not-show-separator':!showSeparator}"
  >
    <PickerFormatDate
      v-model="minValue"
      class="date-picker"
      :disabled="disabled"
      :type="type"
      :format="format"
      :transfer="transfer"
      :disabled-date="dateRange.minDisabled"
      :placeholder="minPlaceholder || $t('common.placeholder.startDate')"
    />
    <slot
      v-if="showSeparator"
      name="separator"
    >
      <span class="separator">&ndash;</span>
    </slot>
    <PickerFormatDate
      v-model="maxValue"
      class="date-picker"
      :disabled="disabled"
      :type="type"
      :format="format"
      :transfer="transfer"
      :disabled-date="dateRange.maxDisabled"
      :placeholder="maxPlaceholder || $t('common.placeholder.endDate')"
    />
  </div>
</template>


<style lang="less" scoped>
.day-range-picker {
  display: flex;
  align-items: center;
  min-width: 180px;

  .date-picker {
    flex: 1;
  }

  .separator {
    flex-shrink: 0;
    margin: 0 10px;
  }
}

.not-show-separator{
  gap:5px;
}
</style>
