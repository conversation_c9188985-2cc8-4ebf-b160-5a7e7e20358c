// box
@lens: 1 2 3 4 5 6 7 8 9 10 12 15 20 25 30 35 40 45 50 60 70 80 90 100 110 120;
each(@lens, {
  // margin
  .m-@{value} {
    margin: unit(@value, px);
  }

  .ml-@{value} {
    margin-left: unit(@value, px);
  }

  .mr-@{value} {
    margin-right: unit(@value, px);
  }

  .mt-@{value} {
    margin-top: unit(@value, px);
  }

  .mb-@{value} {
    margin-bottom: unit(@value, px);
  }

  .mx-@{value} {
    margin-left: unit(@value, px);
    margin-right: unit(@value, px);
  }

  .my-@{value} {
    margin-top: unit(@value, px);
    margin-bottom: unit(@value, px);
  }

  // padding
  .pl-@{value} {
    padding-left: unit(@value, px);
  }

  .pr-@{value} {
    padding-right: unit(@value, px);
  }

  .pt-@{value} {
    padding-top: unit(@value, px);
  }

  .pb-@{value} {
    padding-bottom: unit(@value, px);
  }

  .px-@{value} {
    padding-left: unit(@value, px);
    padding-right: unit(@value, px);
  }

  .py-@{value} {
    padding-top: unit(@value, px);
    padding-bottom: unit(@value, px);
  }

});

.mt-auto {
  margin-top: auto;
}

.mb-auto {
  margin-bottom: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-auto {
  margin-right: auto;
}

// width
@widths: 10 20 30 40 50 60 80 90 100 110 120 130 140 150 160 200 220 250 300 350 400 500 600 700 750 770 786 1220;
each(@widths, {
  .w-@{value} {
    width: unit(@value, px);

    &.ivu-select,
    &.ivu-input-wrapper,
    &.ivu-input-group {
      width: unit(@value, px);
    }
  }

  .min-w-@{value} {
    min-width: unit(@value, px);
  }

  .h-@{value} {
    height: unit(@value, px);
  }
});

// gap
@gaps: 10 15 20 25 30 35 40 80 100;
each(@gaps, {
  .gap-list-@{value} {
    display: flex;

    .gap-item {
      &:not(:last-child) {
        margin-right: unit(@value, px);
      }
    }
  }
  
  ol.gap-list-@{value} {
    display: flex;

    // &:not(.align-top) {
    //   align-items: center;
    // }

    > li {
      &:not(:last-child) {
        margin-right: unit(@value, px);
      }
    }
  }
});

// font-size
@font-sizes: 12 14 16 24;
each(@font-sizes, {
  .fs-@{value} {
    font-size: unit(@value, px);
  }
});

.colon {
  display: flex;

  &::after {
    padding-right: 4px;
    padding-left: 2px;
    content: ':';
  }
}

.empty-ph {
  &:empty {
    &::before {
      content: '—';
    }
  }
}


.h-center {
  display: flex;
  justify-content: center;
}

.h-start {
  display: flex;
  justify-content: flex-start;
}

.v-center {
  display: flex;
  align-items: center;
}

.p-relative {
  position: relative;
}

.full-width {
  width: 100%;
}

.w-half {
  width: 50%;
}

.float-r {
  float: right;
}

.float-l {
  float: left;
}

.clear-float {
  &::after {
    display: block;
    clear: both;
    content: '';
  }
}

.space-between {
  display: flex;
  justify-content: space-between;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pre-wrap {
  white-space: pre-wrap;
}

.pre-line {
  white-space: pre-line;
}


@mainColor: #1F65E0;



