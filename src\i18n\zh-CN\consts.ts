import { Status } from '@/consts/status';
import { ApprovalType } from '@/consts/approval-type';
import { NoonType } from '@/consts/leave';

export default {

  status: {
    [Status.PASS]: '审批通过',
    [Status.PENDING]: '待审批',
    [Status.REJECT]: '审批未通过',
    [Status.PROCESSING]: '审批中',
    [Status.CANCEL]: '已撤回',
  },

  approvalType: {
    [ApprovalType.LEAVE]: '请假',
    [ApprovalType.VERIFY]: '销假',
  },

  noonType: {
    [NoonType.AM]: '上午',
    [NoonType.PM]: '下午',
    [NoonType.ALL]: '全天',
  },
};
