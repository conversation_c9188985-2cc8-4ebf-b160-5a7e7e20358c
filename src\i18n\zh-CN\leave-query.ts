import { Status } from '@/consts/status';

export default {
  search: {
    label: {
      college: '学院',
      leaveType: '请假类型',
      applyOrder: '申请单号',
      approvalStatus: '审批状态',
      applier: '申请人',
      applyTime: '申请时间',
      approver: '审批人',
      applyType: '申请类型',
    },

    placeholder: {
      keyword: '请输入姓名、学号进行搜索',
      applier: '姓名、学号、账号',
    },

  },

  columns: {
    applyOrder: '申请单号',
    nameOrStudId: '姓名/学号',
    college: '学院',
    applyType: '申请类型',
    leaveType: '请假类型',
    leaveTime: '请假时间',
    leaveDuration: '请假时长',
    leaveReason: '请假事由',
    approvalStatus: '审批状态',
    operatorAndTime: '最后操作人/时间',
  },

  content: {
    days: '{days}天',
    hours: '{hours}小时',
  },

  actions: {
    export: '导出',
  },

  status: {
    [Status.PASS]: '审批通过',
    [Status.PENDING]: '待审批',
    [Status.REJECT]: '审批不通过',
  },


};
