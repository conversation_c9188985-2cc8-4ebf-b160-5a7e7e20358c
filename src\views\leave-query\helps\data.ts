import { namespaceT } from '@/helps/namespace-t';
import { LeaveVO } from '../type/list';

export const createSimpleSearchModel = ():SearchModel => ({
  deptId: null,
  applyType: null,
  keyword: '',
});
export const createAdvanceSearchModel = ():AdvanceSearchModel => ({
  deptId: null,
  leaveTypeId: null,
  approvalStatus: null,
  sn: null,
  applyUserName: null,
  lastApprover: null,
  applyType: null,
  startDate: null,
  endDate: null,
});

export const createApprovalModal = ():ApprovalModal => ({
  remark: null,
});


export const useApprovalModal = ():UseApprovalModalType => {
  return {
    model: createApprovalModal(),
    visible: false,
  };
};


export const createModalRules = () => {
  const t = namespaceT('myApproval.modal.hints');
  return {
    remark: [
      { required: true, message: t('remark'), trigger: 'change' },
    ],
  };
};

interface ApproverType {
  id:number,
  [property: string]: any;
}

interface ParamsPageType {
  page?: number;
  limit?:number
}

export type ParamsQueryType = AdvanceSearchModel & ParamsPageType;


export interface SearchModel {
  deptId: string;
  applyType:string,
  keyword: string;
}
export interface AdvanceSearchModel {
  deptId: string;
  leaveTypeId: string;
  approvalStatus: string;
  sn: string;
  applyUserName: string;
  lastApprover?:ApproverType,
  lastApproverId?:number,
  applyType: string;
  startDate: string;
  endDate: string;
}

export type TableRowData = LeaveVO;

export interface ApprovalModal {
  remark:string
}


export interface UseApprovalModalType {
  model: ApprovalModal,
  visible: boolean,
}
