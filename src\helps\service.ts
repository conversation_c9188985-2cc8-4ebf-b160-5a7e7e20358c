/* eslint-disable import/prefer-default-export */
import { QiankunManager } from '@/helps/qiankun';


export function useService() {
  let qkm;

  function mountService(code) {
    qkm.mount(code);
  }

  function unmountService(code) {
    qkm.unmount(code);
  }

  function mountQuickService(code) {
    qkm.mount(code);
  }

  function unmountQuickService(code) {
    qkm.mount(code);
  }

  function registerMicroApps(services, container, lifecycle = {}) {
    services.forEach((service) => {
      qkm.registerApp({
        name: service.code,
        entry: service.serviceUrl,
        props: service.props ?? {},
        container,
        activeRule: () => true,
      }, {}, lifecycle);
    });
  }

  function dispatch(eventCode, payload = {}) {
    qkm.setGlobalState({ eventCode, ...payload });
  }

  function addGlobalEvent(fn) {
    qkm.addEvent(fn);
  }

  function initQkm() {
    qkm = new QiankunManager();
  }

  return {
    mountService,
    mountQuickService,
    unmountService,
    unmountQuickService,
    registerMicroApps,
    initQkm,
    addGlobalEvent,
    dispatch,
  };
}

useService.getInstance = function getInstance() {
  if (this.instance) {
    return this.instance;
  }

  this.instance = useService();
  return this.instance;
};
