<!-- eslint-disable import/order -->
<script lang='ts' setup>
import { getCurrentInstance, ref, provide, onBeforeMount, nextTick } from 'vue';

import TheTheme from './the-theme.vue';
import TheLayout from './the-layout.vue';
import TheHeader from './the-header.vue';
import TheSider from './the-sider.vue';
import ModalAccountError from './biz/modal-account-error.vue';

import { KeepAliveInclude, KeepAliveExclude } from '@/config/keep-alive';
import { useSider } from '@/uses/sider';
import emitter from '@/utils/event-bus';
import { currentLocale } from '@/helps/locale';


const vm = getCurrentInstance();
const locale = currentLocale();
const sider = useSider();
const showView = ref(true);

provide('menuName', sider.getMenuName);
provide('locale', locale);


function bindEvents() {
  // 公共头部聚合了初始数据，在这里监听数据获取情况
  emitter.on('userDataFetched', ({ error, operations }) => {
    if (error) {
      return;
    }

    vm.proxy.$setAuths(operations);
  });


  const reload = () => {
    window.location.reload();
  };

  // 如果接口出现用户未认证错误，则刷新界面
  emitter.on('userUnauthorizedError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('invaildAccessTokenError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('invaildRefreshTokenError', () => {
    reload();
  });
}

function rebuildView() {
  showView.value = false;
  nextTick(() => {
    showView.value = true;
  });
}

onBeforeMount(() => {
  bindEvents();
});

</script>


<template>
  <TheTheme :locale="locale">
    <TheLayout :has-sider="sider.isShow.value">
      <template #header>
        <TheHeader />
      </template>

      <template #sider>
        <TheSider
          v-show="sider.isShow.value"
          @rebuild="rebuildView()"
        />
      </template>

      <template #main>
        <RouterView
          v-if="$hasAuths() && showView"
          v-slot="{ Component, route }"
        >
          <KeepAlive
            :include="KeepAliveInclude"
            :exclude="KeepAliveExclude"
          >
            <component
              :is="Component"
              :key="route.fullPath"
            />
          </KeepAlive>
        </RouterView>
      </template>
    </TheLayout>

    <ModalAccountError />
  </TheTheme>
</template>
