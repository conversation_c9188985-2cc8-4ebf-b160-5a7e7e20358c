import _ from 'lodash';
import { endOfDay } from 'date-fns';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { ParamsQueryType } from './data';

export const handleListData = (data:ParamsQueryType):ParamsQueryType => {
  const cloneData = _.cloneDeep(data);
  const t = namespaceT('dateFormat');

  if (cloneData.startDate && cloneData.endDate) {
    cloneData.startDate = dateFormatSTZ(cloneData.startDate, t('fullDateTime'));
    cloneData.endDate = dateFormatSTZ(endOfDay(cloneData.endDate as unknown as Date), t('fullDateTime'));
  }

  cloneData.lastApproverId = cloneData.lastApprover ? cloneData.lastApprover.id : null;

  return _.omit(cloneData, ['lastApprover']);
};


export const handleExportData = (query:ParamsQueryType, selections:number[]):ParamsQueryType => {
  const t = namespaceT('dateFormat');
  const params = _.cloneDeep(query);

  if (query.page) {
    Object.assign(params, query.page);
  }

  if (Array.isArray(selections) && selections.length) {
    Object.assign(params, {
      ids: [...selections],
    });
  }


  if (params.startDate && params.endDate) {
    params.startDate = dateFormatSTZ(params.startDate, t('fullDateTime'));
    params.endDate = dateFormatSTZ(endOfDay(params.endDate as unknown as Date), t('fullDateTime'));
  }

  params.lastApproverId = params.lastApprover ? params.lastApprover.id : null;

  return _.omit(params, ['lastApprover']);
};
