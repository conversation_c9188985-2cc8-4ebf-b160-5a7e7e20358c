import { namespaceT } from '@/helps/namespace-t';


export function createColumns() {
  const t = namespaceT('leaveType.columns');

  return [
    {
      type: 'selection',
      width: 60,
      align: 'center',
    },
    // 类型名称
    {
      title: t('typeName'),
      key: 'name',
    },
    // 单次最大请假时长
    {
      title: t('maxLeaveDurationForSignal'),
      slot: 'maxLeaveDurationForSignal',
    },
    // 累计最多请假时长
    {
      title: t('maxCumulativeLeaveDuration'),
      slot: 'maxCumulativeLeaveDuration',
    },
    // 适用范围
    {
      title: t('scope'),
      slot: 'scope',
    },

    //  是否启用
    {
      title: t('isEnabled'),
      slot: 'isEnabled',
      width: 80,
    },

    // 假期名称
    {
      title: t('leaveName'),
      slot: 'leaveSubTypeName',
    },

    //  操作
    {
      title: t('operation'),
      slot: 'operation',
    },

  ];
}
