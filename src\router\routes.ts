import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import TheRoot from '@/components/the-root.vue';
import { Auth } from '@/config/auth';


export const routes = [
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [
      {
        path: 'my-approval',
        name: RN.MyApproval,
        component: () => import('@/views/my-approval'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.MyApproval],
          baseAuthCodes: [Auth.MyApproval.View],
        },
      },
      {
        path: 'approval-detail/:id',
        name: RN.ApprovalDetail,
        component: () => import('@/views/my-approval/detail'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.MyApproval],
        },
      },
      {
        path: 'leave-query',
        name: RN.LeaveQuery,
        component: () => import('@/views/leave-query'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LeaveQuery],
          baseAuthCodes: [Auth.LeaveQuery.View],
        },
      },
      {
        path: 'query-detail/:id',
        name: RN.QueryDetail,
        component: () => import('@/views/leave-query/detail'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LeaveQuery],
        },
      },
      {
        path: 'leave-type',
        name: RN.LeaveType,
        component: () => import('@/views/leave-type'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LeaveType],
          baseAuthCodes: [Auth.LeaveType.View],
        },
      },
      // 假期配置
      {
        path: ':id/leave-config',
        name: RN.LeaveConfig,
        component: () => import('@/views/leave-type/leave-config/leave-config.vue'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.LeaveType],
          baseAuthCodes: [Auth.LeaveType.View],
        },
      },
      {
        path: 'special-date',
        name: RN.SpecialDate,
        component: () => import('@/views/special-date'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.SpecialDate],
          baseAuthCodes: [Auth.SpecialDate.View],
        },
      },


      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
];
