import { ApprovalType } from '@/consts/approval-type';

/**
 * LeaveReceiverVO，请假接收人
 */
export interface LeaveReceiverVO {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 请假天数
   */
  days?: number;
  /**
   * 申请部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 请假结束时间
   */
  endDate?: Date;
  /**
   * 请假结束值
   */
  endValue?: string;
  /**
   * 请假小时
   */
  hours?: number;
  /**
   * id
   */
  id?: number;
  /**
   * 请假类型ID
   */
  leaveTypeId?: number;
  /**
   * 请假类型名称
   */
  leaveTypeName?: string;
  /**
   * 请假原因
   */
  reason?: string;
  /**
   * 申请编号
   */
  sn?: string;
  /**
   * 请假开始时间
   */
  startDate?: Date;
  /**
   * 请假开始值
   */
  startValue?: string;
  /**
   * 审批状态 数据字典：LEAVE_RECEIVER_STATUS
   */
  status?: string;
  /**
   * 申请人英文名
   */
  userEnName?: string;
  /**
   * 申请人姓名
   */
  userName?: string;
  /**
   * 申请人工号
   */
  userNo?: string;
  applyType?: ApprovalType;
  [property: string]: any;
}
