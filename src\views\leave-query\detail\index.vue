<script lang="ts" setup>
import { onMounted, onBeforeUnmount } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import { useService } from '@/helps/service';
import { getToken } from '@/helps/get-token';
import { goBack } from '@/helps/navigation';
import { currentLocale } from '@/helps/locale';

const SERVICE_CODE = 'LeaveQueryDetail';
const MOUNT_ID = '#leave-query-detail';
const service = useService.getInstance();
const route = useRoute();
const router = useRouter();
function onGoBack() {
  goBack(router);
}

function injectProps(services) {
  return services.map((item) => ({
    ...item,
    props: {
      state: {
        getToken,
        goBack: onGoBack,
        injectStyle: true,
        dataId: +route.params.id,
        locale: currentLocale(),
      },
    },
  }));
}

onMounted(() => {
  service.initQkm();
  service.registerMicroApps(injectProps([{
    code: SERVICE_CODE,
    // serviceUrl: process.env.MICROSERVICE_LEAVE_QUERY_URL,
    serviceUrl: 'http://127.0.0.1:7113/student-leave-query-detail-js/',
  }]), MOUNT_ID);

  service.mountService(SERVICE_CODE);
});

onBeforeUnmount(() => {
  service.unmountService(SERVICE_CODE);
});


</script>


<template>
  <div class="wrapper">
    <div
      id="leave-query-detail"
    />
  </div>
</template>


<style scoped lang="less">
.wrapper {
  position: relative;
}

#leave-query-detail {
  height: calc(100vh - 50px);

  :deep(> div) {
    width: 100%;
    height: 100%;
  }
}
</style>
