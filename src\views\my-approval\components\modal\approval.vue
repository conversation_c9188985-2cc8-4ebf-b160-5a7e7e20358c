
<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue';

import ModalForm from '@/components/common/modal-form.vue';

import { ApprovalApi } from '@/api/my-approval/approval';
import { ApprovalType } from '@/consts/approval-type';
import { ApprovalResult } from '@/consts/approval-result';
import { ApprovalChannel } from '@/consts/approval-channel';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { UseApprovalModalType, createModalRules } from '../../helps/data';


const props = defineProps<{
  detail:{
    id:number,
    type:ApprovalResult
    leaveType:ApprovalType
  },
}>();

const emit = defineEmits<{
  'on-success':[]
}>();


const approvalModal = defineModel<UseApprovalModalType>();
const t = namespaceT('myApproval.modal');
const tc = namespaceT('common');

const formRef = ref();
const loading = ref(false);

const isAgree = computed(() => props.detail.type === ApprovalResult.AGREE);

const i18nInfo = computed(() => {
  if (isAgree.value) {
    return {
      title: t('title.agree'),
      label: t('label.agreeRemark'),
    };
  }
  return {
    title: t('title.reject'),
    label: t('label.rejectRemark'),
  };
});

const agreeContent = computed(() => {
  if (props.detail.leaveType === ApprovalType.LEAVE) {
    return t('content.leave');
  }
  return t('content.revokeLeave');
});


const onCancel = () => {
  approvalModal.value.visible = false;
};

const onApproval = async () => {
  try {
    loading.value = true;
    const params = {
      id: props.detail.id,
      status: props.detail.type,
      remark: approvalModal.value.model.remark,
      approvalChannel: ApprovalChannel.PC,
    };
    const api = new ApprovalApi({});
    api.params = params;
    await api.send();
    openToastSuccess(tc('hint.success'));
    emit('on-success');
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};


const onSubmit = () => {
  if (isAgree.value) {
    onApproval();
    return;
  }

  formRef.value.validate(async (valid) => {
    if (valid) {
      onApproval();
    }
  });
};


watch(() => approvalModal.value.visible, async (val) => {
  if (val) {
    await nextTick();
    formRef.value.resetFields();
  }
}, {
  immediate: true,
});

</script>

<template>
  <ModalForm
    v-model="approvalModal.visible"
    :title="i18nInfo.title"
    @on-cancel="onCancel"
  >
    <template #content>
      <Form
        v-if="!isAgree"
        ref="formRef"
        :model="approvalModal.model"
        :rules="createModalRules()"
      >
        <FormItem
          :label="i18nInfo.label"
          prop="remark"
        >
          <Input
            v-model="approvalModal.model.remark"
            class="pima-input-type-textarea"
            type="textarea"
            :placeholder="t('placeholder',{num:250})"
            :maxlength="250"
            :rows="4"
          />
        </FormItem>
      </Form>

      <div
        v-else
        class="content-text"
      >
        {{ agreeContent }}
      </div>
    </template>
    <template #button>
      <Button
        class="pima-btn"
        :disabled="loading"
        @click="onCancel"
      >
        {{ t('actions.cancel') }}
      </Button>

      <Button
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        {{ t(`actions.${isAgree ? 'confirm' : 'submit'}`) }}
      </Button>
    </template>
  </ModalForm>
</template>
