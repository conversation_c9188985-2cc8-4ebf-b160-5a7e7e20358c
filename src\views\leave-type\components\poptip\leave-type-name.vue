
<script setup lang="ts">
import { computed } from 'vue';
import { namespaceT } from '@/helps/namespace-t';

import { LeaveSubTypeVO } from '../../type/list';

const props = withDefaults(defineProps<{
  typeList: LeaveSubTypeVO[]
}>(), {
  typeList: () => [],
});


const t = namespaceT('leaveType');

const isTooMuch = computed(() => {
  return Array.isArray(props.typeList) && props.typeList.length > 5;
});

const popTipList = computed(() => {
  return props.typeList.slice(0, 5);
});

</script>


<template>
  <template v-if="!isTooMuch">
    <span
      v-for="type in popTipList"
      :key="type.id"
      class="type-name"
    >
      {{ type.name }}
      <span
        v-if="!type.isEnable"
        class="disable-text"
      >
        {{ t('content.disenable') }}
      </span>
    </span>
  </template>

  <Poptip
    v-else
    trigger="hover"
    transfer
  >
    <template #content>
      <p
        v-for="type in typeList"
        :key="type.id"
        class="type-name"
      >
        {{ type.name }}
        <span
          v-if="!type.isEnable"
          class="disable-text"
        >
          {{ t('content.disenable') }}
        </span>
      </p>
    </template>
    <span
      v-for="type in popTipList"
      :key="type.id"
      class="type-name"
    >
      {{ type.name }}
      <span
        v-if="!type.isEnable"
        class="disable-text"
      >
        {{ t('content.disenable') }}
      </span>

    </span>
    ...
  </Poptip>
</template>


<style lang="less" scoped>
.type-name{
  &:not(:last-child)::after{
    display:inline-block;
    content:"，";
  }

  .disable-text{
    color: var(--primary-color);
  }
}
</style>
