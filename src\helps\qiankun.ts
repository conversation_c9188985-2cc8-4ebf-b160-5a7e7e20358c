import { createSingleton } from '@/helps/record';


const task = [];

class Qiankun {
  allApps = [];

  action = null;

  events = [];

  mountedApps = new Map();

  loadMicroAppFn;

  constructor() {
    import('qiankun').then(({ loadMicroApp, initGlobalState }) => {
      this.loadMicroAppFn = loadMicroApp;
      this.action = initGlobalState({ eventCode: null, payload: null });

      this.action?.onGlobalStateChange((state, prev) => {
        this.events.forEach((fn) => {
          fn(state, prev);
        });
      });
    });
    if (typeof window === 'object' && task) {
      while (task.length > 0) {
        task.shift()();
      }
    }
  }

  unmount(key) {
    const apps = this.mountedApps.get(key);

    if (!apps) {
      return;
    }

    apps.forEach((app) => {
      app.unmount();
    });
    this.mountedApps.delete(key);
  }

  mount(key) {
    if (!this.loadMicroAppFn) {
      task.push(() => {
        this.mount(key);
      });
    }

    if (this.mountedApps.has(key)) {
      return;
    }

    const apps = this.allApps.filter((app) => app.app.name === key);

    if (!apps) {
      throw new Error(`[qiankun] ${String(key)} not found`);
    }

    const events = apps.map((app, index) => new Promise((resolve) => {
      setTimeout(() => {
        this.load(app.app, app.configuration, app.lifeCycles).then((res) => {
          resolve(res);
        });
      }, index * 100);
    }));

    Promise.all(events).then((res) => {
      this.mountedApps.set(key, res);
    });
  }

  async load(app, configuration, lifeCycles) {
    let loadMicroApp = this.loadMicroAppFn;

    if (!loadMicroApp) {
      loadMicroApp = (await import('qiankun')).loadMicroApp;
    }

    return loadMicroApp(app, {
      singular: false,
      sandbox: {
        experimentalStyleIsolation: true,
      },
      ...configuration,
    }, lifeCycles);
  }

  registerApp(app, configuration, lifeCycles) {
    const index = this.allApps.findIndex((app_) => app_.app.name === app.name);
    const newApp = {
      app,
      configuration,
      lifeCycles,
    };

    if (index === -1) {
      this.allApps.push(newApp);
    } else {
      this.allApps.splice(index, 1, newApp);
    }
  }

  setGlobalState(data) {
    this.action?.setGlobalState(data);
  }

  addEvent(fn) {
    this.events.push(fn);
  }
}

export const QiankunManager = createSingleton(Qiankun);
