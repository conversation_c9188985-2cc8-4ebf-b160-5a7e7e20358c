export enum LeaveUnit {
  DAY = 'day',
}


export enum EnableStatus {
  ENABLE = 'enable',
  DISABLE = 'disable',
}


// 页面类型
export enum DetailPageType {
  MODIFY = 'modify',
  ADD = 'add',
}


// 附件格式
export enum AttachmentFormate {
  PDF = 'pdf',
  PNG = 'png',
  JPG = 'jpg',
  DOC = 'doc',
  DOCX = 'docx',
  xls = 'xls',
  xlsx = 'xlsx',
}

export enum IsNeedAttachment {
  YES = 'yes',
  NO = 'no',
}


export enum ApiArgumentScopeType {
  ALL = 'all',
  DEPARTMENT = 'dept',
}
