<script setup lang="ts">
import { onMounted } from 'vue';

import { useDataTagApprovalTypeStatus } from '@/store/data-tag/approval-type';

const store = useDataTagApprovalTypeStatus();


onMounted(() => {
  store.loadDataIfNeeded();
});


</script>


<template>
  <Select
    class="pima-select"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
