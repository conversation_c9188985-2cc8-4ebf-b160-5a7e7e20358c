/* eslint-disable no-param-reassign */
function getPropByPath(obj, path) {
  let tempObj = obj;
  path = path.replace(/\[(\w+)\]/g, '.$1');
  path = path.replace(/^\./, '');

  const keyArr = path.split('.');
  let i = 0;

  // eslint-disable-next-line no-plusplus
  for (let len = keyArr.length; i < len - 1; ++i) {
    const key = keyArr[i];
    if (key in tempObj) {
      tempObj = tempObj[key];
    } else {
      throw new Error('[View UI warn]: please transfer a valid prop path to form item!');
    }
  }
  return {
    o: tempObj,
    k: keyArr[i],
    v: tempObj[keyArr[i]],
  };
}

function resetObject(obj) {
  Object.keys(obj).forEach((key) => {
    if (Object.prototype.toString.call(obj[key]).includes('Object') && obj[key] !== null) {
      resetObject(obj[key]);
    } else if (Object.prototype.toString.call(obj[key]).includes('Boolean')) {
      obj[key] = false;
    } else {
      obj[key] = null;
    }
  });
}


function resetField() {
  this.validateState = '';
  this.validateMessage = '';

  const { model } = this.FormInstance;
  const value = this.fieldValue;
  let path = this.prop;
  if (path.indexOf(':') !== -1) {
    path = path.replace(/:/, '.');
  }

  const prop = getPropByPath(model, path);

  if (Array.isArray(value) && this.initialValue !== null) {
    this.validateDisabled = true;
    prop.o[prop.k] = [].concat(this.initialValue);
  } else if (Object.prototype.toString.call(value).includes('Object')) {
    this.validateDisabled = true;
    resetObject(prop.o[prop.k]);
  } else {
    this.validateDisabled = true;
    prop.o[prop.k] = this.initialValue;
  }
}

export function resetFields() {
  this.fields.forEach((field) => {
    field.resetField = resetField;
    field.resetField();
  });
}
