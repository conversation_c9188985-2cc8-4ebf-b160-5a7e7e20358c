import { Status } from '@/consts/status';
import { namespaceT } from '@/helps/namespace-t';
import { Color } from '@/consts/color';

const t = namespaceT('consts.status');

export const getApprovalStatusText = (status:Status) => {
  const map = new Map(
    Object.values(Status).map((item:Status) => [item, t(item)]),
  );

  if (map.has(status)) {
    return map.get(status);
  }
  return '';
};

export const getApprovalStatusColor = (status:Status) => {
  const map = new Map([
    [Status.PENDING, Color.ORANGE],
    [Status.PROCESSING, Color.ORANGE],
    [Status.PASS, Color.GREEN],
    [Status.REJECT, Color.RED],
  ]);

  if (map.has(status)) {
    return map.get(status);
  }
  return '';
};
