<script setup lang="ts">
import { ref, nextTick, watch, onMounted } from 'vue';

import ModalForm from '@/components/common/modal-form.vue';
// eslint-disable-next-line import/order
import FormDetail from './type/form-detail.vue';

import { ModifyApi } from '@/api/leave-type/modify';
import { GetDetailApi } from '@/api/leave-type/detail';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { createTypeModel, AddOrModifyTypeModalType } from '../../helps/data';
import { handleModifyOrAddData } from '../../helps/handle-api-data';
import { createModalTypeRules } from '../../helps/rules';
import { resetFields } from '../../helps/form';

const emit = defineEmits<{
  'save-success': []
}>();

const props = withDefaults(defineProps<{
  id?:number
}>(), {
  id: null,
});


const model = ref<AddOrModifyTypeModalType>(createTypeModel());
const t = namespaceT('leaveType');
const tc = namespaceT('common');
const formRef = ref();
const loading = ref(false);
const visible = ref(false);


const onCancel = () => {
  visible.value = false;
};

const changeLoadingStatus = () => {
  loading.value = !loading.value;
};


const onModifyDetail = async () => {
  try {
    changeLoadingStatus();
    const api = new ModifyApi({ id: props.id });
    api.data = handleModifyOrAddData(model.value);
    await api.send();

    openToastSuccess(tc('hint.dataSaved'));
    onCancel();
    emit('save-success');
  } catch (error) {
    openToastError(error.message);
  } finally {
    changeLoadingStatus();
  }
};

const getDetail = async () => {
  try {
    const api = new GetDetailApi({ id: props.id });
    const res = await api.send();
    Object.assign(model.value, res);
  } catch (error) {
    openToastError(error.message);
  }
};


const onValidate = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      onModifyDetail();
    }
  });
};

const handleDeptIdsChange = () => {
  formRef.value.validateField('deptIds');
};


watch(() => visible.value, async (val) => {
  if (val) {
    await nextTick();
    formRef.value.resetFields();
    getDetail();
  }
});

onMounted(() => {
  formRef.value.resetFields = resetFields;
});


defineExpose({
  onOpen: () => {
    visible.value = true;
  },
});

</script>

<template>
  <ModalForm
    v-model="visible"
    :width="800"
    :title="t('modalModifyType.title')"
    @on-cancel="onCancel"
  >
    <template #content>
      <Form
        ref="formRef"
        :model="model"
        :rules="createModalTypeRules()"
        :label-width="150"
        label-colon
      >
        <FormDetail
          v-model="model"
          @scope-change="handleDeptIdsChange"
        />
      </Form>
    </template>
    <template #button>
      <Button
        class="pima-btn"
        :disabled="loading"
        @click="onCancel"
      >
        {{ tc('action.cancel') }}
      </Button>

      <Button
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click="onValidate"
      >
        {{ tc('action.save') }}
      </Button>
    </template>
  </ModalForm>
</template>
