import { CommonApi } from '../common/common-api';
// eslint-disable-next-line import/order
import { handleGetDetailData } from '@/views/leave-type/helps/handle-api-data';

export class GetDetailApi extends CommonApi {
  id:number;

  constructor(args) {
    super({});
    this.id = args.id;
  }

  url() {
    return `/leave-types/${this.id}`;
  }

  async send() {
    const { model } = await super.send();

    return handleGetDetailData(model);
  }
}
