<script setup lang="ts">
import { getCurrentInstance } from 'vue';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';

import { namespaceT } from '@/helps/namespace-t';
import { getSpecialDateStatusText, getSpecialDateTypeText } from '@/utils/special-date';

import { SpecialDateStatus } from '@/consts/special-date';
import { openToastError } from '@/helps/toast';
import { createColumns } from './columns';
import { TableRowData } from '../helps/data';


withDefaults(defineProps<{
  data: TableRowData[],
  loading: boolean,
}>(), {
  data: null,
  loading: false,
});

const emit = defineEmits<{
  'on-modify':[id:number],
  'on-delete':[id:number]
}>();


const t = namespaceT('specialDate');
const vm = getCurrentInstance();


const onModify = (row:TableRowData) => {
  emit('on-modify', row.id);
};

const onDelete = (row:TableRowData) => {
  if (row.isEnable === SpecialDateStatus.ENABLE) {
    openToastError(t('hint.disabledDeleteDataForEnabled'));
    return;
  }

  emit('on-delete', row.id);
};


const actions = [
  {
    label: t('actions.modify'),
    triggerEvent: onModify,
    can: vm.proxy.$can((P) => P.SpecialDate.Modify),
  },
  {
    label: t('actions.delete'),
    triggerEvent: onDelete,
    can: vm.proxy.$can((P) => P.SpecialDate.Delete),
  },
];

</script>


<template>
  <CTable
    v-bind="$attrs"
    :columns="createColumns()"
    :data="data"
    :loading="loading"
  >
    <!-- 请假时间 -->
    <template #date="{row}">
      {{ row.startDate }}
      ~
      {{ row.endDate }}
    </template>

    <!-- 类型 -->
    <template #type="{row}">
      {{ getSpecialDateTypeText(row.type) }}
    </template>

    <!-- 时长 -->
    <template #duration="{row}">
      {{ t('content.day',{day: row.days }) }}
    </template>

    <!-- 状态 -->
    <template #status="{row}">
      {{ getSpecialDateStatusText(row.isEnable) }}
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>
</template>
