import { handleConfigDetailData } from '@/views/leave-type/helps/handle-api-data';
import { CommonApi } from '../../common/common-api';

export class GetDetailApi extends CommonApi {
  id:number;

  constructor(args) {
    super({});
    this.id = args.id;
  }

  url() {
    return `/leave-types/${this.id}/sub`;
  }

  async send() {
    const { model } = await super.send();

    if (model && Array.isArray(model.leaveSuTypes) && model.leaveSuTypes.length > 0) {
      const list = handleConfigDetailData(model.leaveSuTypes);
      return list;
    }

    return null;
  }
}
