<template>
  <Select
    class="pima-select"
    @on-change="onChange"
  >
    <Option
      v-for="item in data"
      :key="item.value"
      :value="item.value"
      :label="item.label"
    />
  </Select>
</template>


<script lang='ts'>
import { defineComponent, ref, watch } from 'vue';
import _ from 'lodash';
import { YoN } from '@/consts/y-o-n';
import { i18n } from '@/i18n';


export default defineComponent({
  name: 'SelectBool',

  props: {
    value: {
      type: [Boolean, Number, String],
      default: undefined,
    },

    trueValue: {
      type: [Boolean, Number, String],
      default: true,
    },

    trueText: {
      type: String,
      default: i18n.global.t('consts.yon.y'),
    },

    falseValue: {
      type: [Boolean, Number, String],
      default: false,
    },

    falseText: {
      type: String,
      default: i18n.global.t('consts.yon.n'),
    },
  },

  emits: [
    'input', 'on-change',
  ],

  setup(props, { emit }) {
    const valueMap = new Map([
      [props.trueValue, YoN.Y],
      [props.falseValue, YoN.N],
    ]);
    const recoverMap = new Map();
    valueMap.forEach((value, key) => {
      recoverMap.set(value, key);
    });
    const textMap = new Map([
      [props.trueValue, props.trueText],
      [props.falseValue, props.falseText],
    ]);

    const keyMap = valueMap.keys();
    const options = [];
    let result = keyMap.next();
    while (!result.done) {
      options.push({
        value: valueMap.get(result.value),
        label: textMap.get(result.value),
      });
      result = keyMap.next();
    }
    const data = Object.freeze(options);

    const valueShadow = ref();


    watch(
      () => props.value,
      (value) => {
        if (!_.isNil(value) && valueMap.get(value) !== valueShadow.value) {
          valueShadow.value = valueMap.get(value);
        }
      },
      {
        immediate: true,
      },
    );


    function onInput(value) {
      const recoverValue = recoverMap.get(value);
      emit('input', recoverValue);
    }

    function onChange(value) {
      const recoverValue = recoverMap.get(value);
      emit('on-change', recoverValue);
    }


    return {
      data,
      valueShadow,

      onInput,
      onChange,
    };
  },
});
</script>
