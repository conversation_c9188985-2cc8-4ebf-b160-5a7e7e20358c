import _ from 'lodash';

import { ApiArgumentScopeType, AttachmentFormate, EnableStatus, IsNeedAttachment } from '@/consts/leave-type';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { LeaveTypeVO as DetailType } from '../type/detail';
import {
  AddOrModifyTypeModalType,
  LeaveConfigType,
  LeaveConfigModelType,
  createFormModel,
  createLeaveConfigItem,
} from './data';

export const handleModifyOrAddData = (model:AddOrModifyTypeModalType) => {
  const td = namespaceT('dateFormat');
  const cloneData = _.cloneDeep(model);
  cloneData.deptIds = cloneData.deptIds.map((item) => item.id);


  Object.assign(cloneData, {
    leaveEndTime: model.leaveTime.notLimit ? null : dateFormatSTZ(cloneData.leaveTime.leaveEndTime, td('date')),
    leaveStartTime: model.leaveTime.notLimit ? null : dateFormatSTZ(cloneData.leaveTime.leaveStartTime, td('date')),
    isLimitLeaveTime: !model.leaveTime.notLimit,
    maxLeaveAmtPerPeriod: !cloneData.maxLeaveAmtPerPeriods.limit ? cloneData.maxLeaveAmtPerPeriods.days : null,
    isLimitLeaveAmt: !cloneData.maxLeaveAmtPerPeriods.limit,
    maxLeaveLength: cloneData.maxLeaveLength ? cloneData.maxLeaveLength : null,
    isEnable: cloneData.isEnable === EnableStatus.ENABLE,
    scope: ApiArgumentScopeType.DEPARTMENT,
  });


  return _.omit(cloneData, ['leaveTime', 'maxLeaveAmtPerPeriods', 'code']);
};


export const handleGetDetailData = (model:DetailType) => {
  const cloneData = _.cloneDeep(model);

  Object.assign(cloneData, {
    deptIds: cloneData.deptDataList.map((item) => ({
      id: item.deptId,
      name: item.deptName,
    })),
    leaveTime: {
      leaveStartTime: cloneData.leaveStartTime,
      leaveEndTime: cloneData.leaveEndTime,
      notLimit: !cloneData.isLimitLeaveTime,
    },

    maxLeaveAmtPerPeriods: {
      days: cloneData.isLimitLeaveAmt ? cloneData.maxLeaveAmtPerPeriod : 0,
      limit: !cloneData.isLimitLeaveAmt,
    },
    isNeedAtt: cloneData.isNeedAtt ? IsNeedAttachment.YES : IsNeedAttachment.NO,
    isEnable: cloneData.isEnable ? EnableStatus.ENABLE : EnableStatus.DISABLE,
  });


  const reservedKey = Object.keys(createFormModel());

  return _.pick(cloneData, reservedKey);
};


// 假期配置  提交
export const handleConfigModifyData = (model:LeaveConfigType) => {
  const cloneData = _.cloneDeep(model);
  const list = cloneData.list.map((item) => {
    const cloneItem = _.cloneDeep(item);
    if (cloneItem.isNeedAtt === IsNeedAttachment.YES) {
      cloneItem.attExt = (cloneItem.attExt as AttachmentFormate[]).join(',');

      return {
        ..._.omit(cloneItem, ['uuid', 'code']),
        isNeedAtt: true,
        isEnable: cloneItem.isEnable === EnableStatus.ENABLE,
      };
    }

    return {
      ..._.omit(cloneItem, ['uuid', 'code', 'attExt', 'attSize']),
      isNeedAtt: false,
      isEnable: cloneItem.isEnable === EnableStatus.ENABLE,
    };
  });

  return {
    subList: list,
  };
};


// 假期配置 详情

export const handleConfigDetailData = (model:LeaveConfigModelType[]):LeaveConfigModelType[] => {
  const cloneDate = _.cloneDeep(model);
  const list = cloneDate.map((item) => {
    const cloneItem = {
      ...createLeaveConfigItem(),
      ...item,
    };


    if (cloneItem.isNeedAtt) {
      cloneItem.attExt = (cloneItem.attExt as string).split(',') as AttachmentFormate[];
    }

    cloneItem.isEnable = cloneItem.isEnable ? EnableStatus.ENABLE : EnableStatus.DISABLE;
    cloneItem.isNeedAtt = cloneItem.isNeedAtt ? IsNeedAttachment.YES : IsNeedAttachment.NO;

    return cloneItem;
  });

  return list;
};
