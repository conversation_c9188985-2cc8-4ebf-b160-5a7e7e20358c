import { EmptyText } from '@/consts/empty-text';
import { namespaceT } from '@/helps/namespace-t';

/**
 *
 * @param name  i18n consts文件所对应的翻译字段对象名，
 * @param consts consts枚举
 * @returns 获取枚举翻译的函数
 */
export const getConstsText = <T>(name: string, consts: T) => {
  const t = namespaceT(`consts.${name}`);

  const map = new Map<T, string>(
    Object.values(consts).map((item): [T, string] => [item, t(item)]),
  );

  return (key: string) => {
    return map.get(key as T) || EmptyText;
  };
};
