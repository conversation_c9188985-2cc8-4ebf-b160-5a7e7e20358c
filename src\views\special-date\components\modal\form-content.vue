<!-- eslint-disable import/order -->
<script setup lang="ts">
import { watch } from 'vue';

import PimaInput from '@/components/common/pima-input.vue';
import PickerDayRange from '@/components/common/picker/picker-day-range.vue';
import RadioType from '../radio/type.vue';
import RadioStatus from '../radio/status.vue';

import { getDaysBetween } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { FormType } from '../../helps/data';


const emit = defineEmits<{
  'on-add':[],
  'on-remove':[id:string]
}>();

withDefaults(defineProps<{
  isModify:boolean;
}>(), {
  isModify: false,
});

const model = defineModel<FormType>();
const t = namespaceT('specialDate');


watch(() => model.value.time, (val) => {
  if (Array.isArray(val) && val.length > 0) {
    model.value.days = model.value.time.reduce((pre, cur) => {
      if (cur.startDate && cur.endDate) {
        return pre + getDaysBetween(cur.startDate, cur.endDate);
      }
      return pre;
    }, 0);
  }
}, {
  deep: true,
});


</script>


<template>
  <!-- 类型 -->
  <FormItem
    :label="t('columns.type')"
    prop="type"
  >
    <RadioType v-model="model.type" />
  </FormItem>

  <!-- 名称 -->
  <FormItem
    :label="t('columns.name')"
    prop="name"
  >
    <PimaInput v-model="model.name" />
  </FormItem>

  <!-- TODO: 后续启用 -->
  <!-- 休假单位 -->
  <!-- <FormItem
      :label="t('columns.leaveUnit')"
      prop="leaveUnit"
    >
      <RadioLeaveUnit v-model="model.leaveUnit" />
    </FormItem> -->

  <!-- 时间 -->
  <FormItem
    v-for="item in model.time"
    :key="item.id"
    :label="t('columns.time')"
    prop="time"
  >
    <div class="time-wrap">
      <PickerDayRange
        v-model:min="item.startDate"
        v-model:max="item.endDate"
        class="datePicker"
        :show-separator="false"
        transfer
      />
      <div
        v-if="!isModify"
        class="action-wrap"
      >
        <Icon
          type="ios-add-circle-outline"
          color="#1f65e0"
          class="icon mr-5"
          :size="20"
          @click="emit('on-add')"
        />

        <Icon
          v-if="(model.time.length > 1)"
          type="ios-remove-circle-outline"
          color="#d63c3c"
          class="icon"
          :size="20"
          @click="emit('on-remove', item.id)"
        />
      </div>
    </div>
  </FormItem>

  <!-- 时长 -->
  <FormItem
    :label="
      t('columns.duration')"
    prop="days"
  >
    {{ t('content.day',{day:model.days}) }}
  </FormItem>


  <!-- 状态 -->
  <FormItem
    :label="t('columns.status')"
    prop="isEnable"
  >
    <RadioStatus v-model="model.isEnable" />
  </FormItem>
</template>


<style lang="less" scoped>
.time-wrap{
  display: flex;
  gap:10px;
  width: 100%;

  .datePicker{
    flex:1;
  }

  .action-wrap {
      display: flex;
      flex-shrink: 1;
      width: 60px;
      padding-top: 5px;

      .icon {
        cursor: pointer;
      }
    }
}
</style>
