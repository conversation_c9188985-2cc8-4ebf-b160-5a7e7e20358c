import { v4 as uuidV4 } from 'uuid';
import { parseISO } from 'date-fns';

import { SpecialDateStatus } from '@/consts/special-date';

import { CommonApi } from '../common/common-api';

export class GetDetailApi extends CommonApi {
  id:number;

  constructor(args) {
    super({});
    this.id = args.id;
  }

  url() {
    return `/holidays/${this.id}`;
  }

  async send() {
    const { model } = await super.send();
    model.isEnable = model.isEnable ? SpecialDateStatus.ENABLE : SpecialDateStatus.DISABLE;
    model.time = [{
      id: uuidV4(),
      startDate: parseISO(model.startDate),
      endDate: parseISO(model.endDate),
    }];
    return model;
  }
}
