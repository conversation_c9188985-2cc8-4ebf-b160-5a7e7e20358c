import { namespaceT } from '@/helps/namespace-t';


export function createColumns() {
  const t = namespaceT('myApproval.columns');

  return [
    // 申请单号
    {
      title: t('applyOrder'),
      slot: 'applyOrder',
    },
    // 姓名/学号
    {
      title: t('nameOrStudId'),
      slot: 'nameOrStudId',
    },

    // 学院
    {
      title: t('college'),
      key: 'deptName',
    },

    // 审批类型
    {
      title: t('approvalType'),
      slot: 'applyType',
    },

    // 申请时间
    {
      title: t('applyTime'),
      slot: 'applyTime',
    },

    //  请假类型
    {
      title: t('leaveType'),
      key: 'leaveTypeName',
    },
    //  请假时间
    {
      title: t('leaveTime'),
      slot: 'leaveTime',
    },
    //  请假时长
    {
      title: t('leaveDuration'),
      slot: 'days',
    },
    //  请假事由
    {
      title: t('leaveReason'),
      key: 'reason',
    },
    //  审批状态
    {
      title: t('approvalStatus'),
      slot: 'approvalStatus',
    },

    // 操作
    {
      title: t('operation'),
      slot: 'operation',
    },
  ];
}
