<script setup lang="ts">
import { ref } from 'vue';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-add':[]
}>();

const t = namespaceT('specialDate');
const loading = ref(false);


</script>

<template>
  <WrapperSearchSimple>
    <template #right>
      <Button
        v-if="$can(P=>P.SpecialDate.Add)"
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click=" emit('on-add')"
      >
        {{ t('actions.add') }}
      </Button>
    </template>
  </WrapperSearchSimple>
</template>
