import { namespaceT } from './namespace-t';
import { dateFormatSTZ } from './date';
import { getNoonTypeText } from './i18n/noon-type';

/** 请假时间 */
export const handleLeaveTimeText = (date, time) => {
  const td = namespaceT('dateFormat');

  const dateText = dateFormatSTZ(new Date(date), td('date'));

  return `${dateText} ${getNoonTypeText(time)}`;
};


/** 请假时间范围 */
export const handleLeaveTimeRangeText = (data: Record<string, any> = {}): string => {
  const { startDate, startValue, endDate, endValue } = data;
  let timeText = '';

  if (startDate && startValue) {
    timeText += handleLeaveTimeText(startDate, startValue);
  }

  if (endDate && endValue) {
    timeText += ` ~ ${handleLeaveTimeText(endDate, endValue)}`;
  }

  return timeText;
};


/** 界面上展示的请假日期文案 */
export const handleDateText = ({ isGtOneDay, startDate, startValue, endDate, endValue }) => {
  if (!isGtOneDay) {
    return handleLeaveTimeText(startDate, startValue);
  }

  return handleLeaveTimeRangeText({
    startDate,
    startValue,
    endDate,
    endValue,
  });
};


/** 处理 table 列的请假时间文案 */
export const handleRowDateText = (row) => {
  const { isGtOneDay, startDate, endDate, startValue, endValue } = row;
  return handleDateText({
    isGtOneDay,
    startDate,
    endDate,
    startValue,
    endValue,
  });
};
