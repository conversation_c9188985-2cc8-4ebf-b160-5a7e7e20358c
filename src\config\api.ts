import { PUBLIC_PATH } from './public-path';

export const LOCAL_STUDENT_LEAVE_API_BASE_URL = `${PUBLIC_PATH}x-student-leave-api`; // 请假 接口API
export const LOCAL_BDC_CORE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-core-api`; // 公共接口API
export const LOCAL_BDC_ARCH_API_BASE_URL = `${PUBLIC_PATH}x-bdc-arch-api`; // 统一数据接口API
export const LOCAL_BDC_AUTH_API_BASE_URL = `${PUBLIC_PATH}x-bdc-auth-api`; // 统一权限接口API
export const LOCAL_BDC_DFS_API_BASE_URL = `${PUBLIC_PATH}x-bdc-dfs-api`; // 附件管理接口API
export const LOCAL_BDC_EXPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-export-api`; // 导出组件接口API
export const LOCAL_BDC_IMPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-import-api`; // 导入组件接口API
export const LOCAL_BDC_SERVICE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-service-api`; // 统一应用接口API
export const LOCAL_USER_TOKEN_API_BASE_URL = `${PUBLIC_PATH}x-user-token-api`; // 自定义获取token接口

export const API_SALT = 'pkusz_student_leave'; // 接口盐值


// 接口日期格式
export const ApiDateFormat = Object.freeze({
  DATE_TIME: "yyyy-MM-dd'T'HH:mm:ssxxx",
  // DATE_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSS",
  DATE: 'yyyy-MM-dd',
});

// 接口所在服务器时区
export const API_SERVER_TIME_ZONE = 'Asia/Shanghai';
