{
  "compilerOptions": {
    "target": "es2018",
    "module": "CommonJS",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "Node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["node_modules/*"],
    },
     "lib": ["es2015", "esnext", "dom", "dom.iterable", "scripthost"]
  },
  "types": [
    "vue",
    "node",
    "webpack-env",
  ],
  "include": [
    "server/**/*.ts",
    "src/**/*.ts",
    "src/**/*.vue",
    "types",
  ],
  "exclude": [
    "node_modules",
    "build",
  ],
}
