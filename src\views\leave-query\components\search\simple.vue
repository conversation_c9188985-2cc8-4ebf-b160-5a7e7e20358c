<script setup lang="ts">
import { ref } from 'vue';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';
import SelectApplyType from '@/components/biz/select/approval-type.vue';

import { ExportApi } from '@/api/leave-query/export';
import { CommonDepartmentsParams } from '@/consts/common-departments';
import { MaxLength } from '@/consts/max-length';
import { openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';
import { UseQueryTableReturn } from '@/uses/query-table';
import { useImportExport } from '@/uses/import-export';

import { handleExportData } from '../../helps/handle-api-data';
import { ParamsQueryType } from '../../helps/data';

const props = withDefaults(defineProps<{
  selectRows:number[]
}>(), {
  selectRows: null,
});


const emit = defineEmits<{
  'on-search': [],
}>();

const qt = defineModel<UseQueryTableReturn>();


const t = namespaceT('leaveQuery');
const tc = namespaceT('common');
const loading = ref(false);


const exportData = async () => {
  try {
    loading.value = true;

    const api = new ExportApi({});
    api.params = handleExportData((qt.value.query.value as ParamsQueryType), props.selectRows);
    const { model: taskId } = await api.send();

    return { taskId };
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    loading.value = false;
  }
};


const importExport = useImportExport({ exportData });

function onExport() {
  importExport.exportData();
}

</script>

<template>
  <WrapperSearchSimple
    :show="qt.shown.value"
    advanced
    @show-search-advanced="qt.showAdvancedSearch"
  >
    <!--  部门 -->
    <PairLabelItem :label="t('search.label.college')">
      <SelectPimaRemoteDepartment
        v-model="qt.simpleSearchModel.deptId"
        :width="200"
        :placeholder="tc('placeholder.all')"
        :parent-code="CommonDepartmentsParams.Student"
        clearable

        @on-change="emit('on-search')"
      />
    </PairLabelItem>

    <!--  申请类型 -->
    <PairLabelItem :label="t('search.label.applyType')">
      <SelectApplyType
        v-model="qt.simpleSearchModel.applyType"
        clearable
        class="w-200"
        :placeholder="tc('placeholder.all')"
        @on-change="emit('on-search')"
      />
    </PairLabelItem>

    <!-- 搜索框 -->
    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="qt.simpleSearchModel.keyword"
        clearable
        class="w-400"
        :placeholder="t('search.placeholder.keyword')"
        :max-length="MaxLength.INPUT_MAX_LENGTH"
        @on-clear="emit('on-search')"
        @on-search="emit('on-search')"
      />
    </PairLabelItem>

    <template #right>
      <Button
        v-if="$can(P=>P.LeaveQuery.Export)"
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click="onExport"
      >
        {{ t('actions.export') }}
      </Button>
    </template>
  </WrapperSearchSimple>
</template>
