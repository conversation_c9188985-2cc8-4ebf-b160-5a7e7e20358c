import { reactive, ref } from 'vue';

import { pickFiles } from '@/utils/pick-files';
import { useImportExportCenterPlugin } from '@/plugins/import-export-center';

/**
 * @async
 * @function ExecuteFunction
 * @param { File } file - 导入文件时使用
 * @return { Promise<{ taskId: Number, callback: Function }> } - 导入自动刷新和导出任务自动下载，需要返回任务ID。导入完成后，需要回调刷新页面
 */

/**
 * @typedef { object } ImportExportOptions
 * @property { ExecuteFunction } downloadTemplate - 下载导入模板的方法
 * @property { ExecuteFunction } importFile - 导入文件的方法
 * @property { ExecuteFunction } exportData - 导出文件的方法
 */

/**
 * 针对拥有下载导入模板、导入和导出功能的页面设计
 * @param { ImportExportOptions } options
 */
export function useImportExport(options) {
  const downloadingTemplate = ref(false);
  const importing = ref(false);
  const exporting = ref(false);

  async function downloadTemplate() {
    try {
      downloadingTemplate.value = true;
      await options.downloadTemplate();
    } finally {
      downloadingTemplate.value = false;
    }
  }

  async function importFile() {
    pickFiles({
      multiple: false,
      accept: 'excel',
      async onPick(file) {
        try {
          importing.value = true;
          const { taskId, callback } = await options.importFile(file) || {};

          if (Number.isInteger(taskId)) {
            // 以回调函数形式，调用远程导入导出组件方法 - 推荐使用
            useImportExportCenterPlugin((ixc) => {
              ixc.refresh();
              ixc.onceTaskCompleted(taskId, callback);
            });
          }
        } finally {
          importing.value = false;
        }
      },
    });
  }

  async function exportData() {
    try {
      exporting.value = true;
      const { taskId } = await options.exportData() || {};

      if (Number.isInteger(taskId)) {
        // 以Promise形式，调用远程导入导出组件方法
        useImportExportCenterPlugin((ixc) => {
          ixc.refresh();
          ixc.downloadWhenTaskExportCompleted(taskId);
        });
      }
    } finally {
      exporting.value = false;
    }
  }

  return reactive({
    downloadingTemplate,
    importing,
    exporting,

    downloadTemplate,
    importFile,
    exportData,
  });
}
