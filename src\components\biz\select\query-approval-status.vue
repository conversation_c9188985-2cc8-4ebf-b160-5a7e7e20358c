<script setup lang="ts">
import { onMounted } from 'vue';

import { useDataTagQueryApprovalStatus } from '@/store/data-tag-query-approval-status';

const store = useDataTagQueryApprovalStatus();

onMounted(() => {
  store.loadDataIfNeeded();
});


</script>


<template>
  <Select
    class="pima-select"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
