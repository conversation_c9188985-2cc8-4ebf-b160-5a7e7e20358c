<template>
  <RadioGroup
    class="pima-radio-group-vertical"
    :model-value="valueShadow"
    @update:model-value="onInput"
    @on-change="onChange"
  >
    <Radio
      v-for="item in data"
      :key="item.value"
      :label="item.value"
    >
      <span>{{ item.label }}</span>
    </Radio>
  </RadioGroup>
</template>


<script lang='ts'>
import { defineComponent, ref, watch, toRef } from 'vue';
import { YoN } from '@/consts/y-o-n';
import { i18n } from '@/i18n';


export default defineComponent({
  name: 'RadioGroupBool',

  props: {
    modelValue: {
      type: [Boolean, Number, String],
      default: undefined,
    },

    trueValue: {
      type: [Boolean, Number, String],
      default: true,
    },

    trueText: {
      type: String,
      default: i18n.global.t('consts.yon.y'),
    },

    falseValue: {
      type: [Boolean, Number, String],
      default: false,
    },

    falseText: {
      type: String,
      default: i18n.global.t('consts.yon.n'),
    },
  },

  emits: [
    'update:model-value', 'on-change',
  ],

  setup(props, { emit }) {
    const modelValue = toRef(props, 'modelValue');
    const valueMap = new Map([
      [props.trueValue, YoN.Y],
      [props.falseValue, YoN.N],
    ]);
    const recoverMap = new Map();
    valueMap.forEach((value, key) => {
      recoverMap.set(value, key);
    });
    const textMap = new Map([
      [props.trueValue, props.trueText],
      [props.falseValue, props.falseText],
    ]);

    const keyMap = valueMap.keys();
    const options = [];
    let result = keyMap.next();
    while (!result.done) {
      options.push({
        value: valueMap.get(result.value),
        label: textMap.get(result.value),
      });
      result = keyMap.next();
    }
    const data = Object.freeze(options);

    const valueShadow = ref();


    watch(
      modelValue,
      (value) => {
        if (valueMap.get(value) !== valueShadow.value) {
          valueShadow.value = valueMap.get(value);
          if (props.trueValue === true && props.falseValue === false) {
            if (!(value === true || value === false)) {
              valueShadow.value = null;
            }
          }
        }
      },
      {
        immediate: true,
      },
    );


    function onInput(value) {
      const recoverValue = recoverMap.get(value);
      emit('update:model-value', recoverValue);
    }

    function onChange(value) {
      const recoverValue = recoverMap.get(value);
      emit('on-change', recoverValue);
    }


    return {
      data,
      valueShadow,

      onInput,
      onChange,
    };
  },
});
</script>
