<script lang='ts' setup name="ForbiddenPage">
import { defineAsyncComponent, ref, onMounted } from 'vue';

// @ts-expect-error: Cannot find module 'pimaRemoteUI/PimaForbidden' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaForbidden = defineAsyncComponent(() => import('pimaRemoteUI/PimaForbidden'));

const show = ref(false);

onMounted(() => {
  show.value = true;
});


function onGoBack() {
  window.history.back();
}
</script>


<template>
  <PimaForbidden
    v-if="show"
    class="forbidden"
    @go-back="onGoBack()"
  />
</template>


<style lang="less" scoped>
.pimaru-forbidden.forbidden {
  width: 100%;
  height: calc(100vh - 40px);
}
</style>
