import { createRequest } from '@/utils/request';
import { i18n } from '@/i18n';
import { errorInterceptor } from './error-interceptor';


export class BaseRequestApi {
  constructor(args) {
    let baseURL;
    if ('baseURL' in args) {
      ({ baseURL } = args);
    }

    let timeout;
    if ('timeout' in args) {
      ({ timeout } = args);
    }

    let responseType;
    if ('responseType' in args) {
      ({ responseType } = args);
    }

    let headers;
    if ('headers' in args) {
      ({ headers } = args);
    }

    let salt;
    if ('salt' in args) {
      ({ salt } = args);
    }

    this.request = createRequest({
      baseURL,
      timeout,
      responseType,
      headers,
      salt,
      locale: i18n.global.locale.value,
    });
    this.request.interceptors.response.use(
      (response) => response,
      (error) => {
        console.log('%c [ error ]-44', 'font-size:13px; background:#8ae412; color:#ceff56;', error);

        return errorInterceptor(error);
      },
    );

    this.targetParams = this.defaultParams();
    this.targetData = this.defaultData();
  }

  method() {
    return 'GET';
  }

  url() {
    return null;
  }

  defaultParams() {
    return {};
  }

  get params() {
    return this.targetParams;
  }

  set params(value) {
    this.targetParams = {
      ...this.defaultParams(),
      ...value,
    };
  }

  defaultData() {
    return {};
  }

  get data() {
    return this.targetData;
  }

  set data(value) {
    if (value instanceof FormData) {
      this.targetData = value;
      return;
    }

    if (Array.isArray(value)) {
      this.targetData = [
        ...this.defaultData(),
        ...value,
      ];
      return;
    }

    this.targetData = {
      ...this.defaultData(),
      ...value,
    };
  }

  send() {
    return this.request({
      method: this.method().toUpperCase(),
      url: this.url(),
      params: this.targetParams,
      data: this.targetData,
    });
  }
}
