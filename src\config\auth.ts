export const Auth = Object.freeze({
  MyApproval: {
    View: 'VIEW',
    Approval: 'APPROVAL',
    BatchApproval: 'BATCH_APPROVAL',
  },
  LeaveQuery: {
    View: 'QUERY_VIEW',
    Export: 'QUERY_EXPORT',
  },
  LeaveType: {
    View: 'TYPE_VIEW',
    Add: 'TYPE_ADD',
    Modify: 'TYPE_MODIFY',
    Copy: 'TYPE_COPY',
    BatchCopy: 'TYPE_BATCH_COPY',
    Delete: 'TYPE_DELETE',
    BatchEnable: 'TYPE_BATCH_ENABLE',
    BatchStop: 'TYPE_BATCH_STOP',
    BatchDelete: 'TYPE_BATCH_DELETE',
  },
  SpecialDate: {
    View: 'DATE_VIEW',
    Add: 'DATE_ADD',
    Modify: 'DATE_MODIFY',
    Delete: 'DATE_DELETE',
  },
});
