
<script setup lang="ts">
import { watchEffect } from 'vue';

import PickerDayRange from '@/components/common/picker/picker-day-range.vue';

import { namespaceT } from '@/helps/namespace-t';
import { FormModel } from '../../helps/data';


const t = namespaceT('leaveType');
const model = defineModel<FormModel>();


watchEffect(() => {
  if (model.value.maxLeaveAmtPerPeriods.limit) {
    model.value.maxLeaveAmtPerPeriods.days = null;
  }
});

</script>

<template>
  <!-- 可请假时间范围 -->
  <FormItem
    :label="t('label.timeLimitForLeave')"
    prop="leaveTime"
  >
    <PickerDayRange
      v-model:min="model.leaveTime.leaveStartTime"
      v-model:max="model.leaveTime.leaveEndTime"
      class="datePicker w-300"
    >
      <template #separator>
        <span class="separator">{{ t('content.to') }}</span>
      </template>
    </PickerDayRange>
  </FormItem>

  <!-- 最大请假时长 -->
  <FormItem
    :label="t('label.maxDuration.label')"
    prop="code"
  >
    <span class="content-part">
      {{ t('label.maxDuration.content1') }}
    </span>
    <InputNumber
      v-model.trim="model.maxLeaveLength"
      class="w-100 pima-input-number number-input"
      :precision="0"
      :min="0"
      :step="1"
    />
    <span class="content-part">
      {{ t('label.maxDuration.content2') }}
    </span>
  </FormItem>

  <!-- 累计请假时长 -->
  <FormItem
    :label="t('label.cumulativeDuration.label')"
    prop="maxLeaveAmtPerPeriods"
  >
    <div class="maxLeaveAmtPerPeriod">
      <div class="input-part">
        <span class="content-part">
          {{ t('label.cumulativeDuration.content1') }}
        </span>
        <InputNumber
          v-model="model.maxLeaveAmtPerPeriods.days"
          class="w-100 pima-input-number number-input"
          :disabled="model.maxLeaveAmtPerPeriods.limit"
          :precision="0"
          :min="0"
          :step="1"
        />
        <span class="content-part">
          {{ t('label.cumulativeDuration.content2') }}
        </span>
      </div>
      <div class="checkbox-part pima-checkbox-group">
        <Checkbox v-model="model.maxLeaveAmtPerPeriods.limit">
          {{ t('content.notLimit') }}
        </Checkbox>
      </div>
    </div>
  </FormItem>
</template>

<style lang="less" scoped>
.datePicker{
  .separator{
    display: inline-block;
    margin: 0 10px;
  }
}

.number-input{
  margin: 0 10px ;
}

.maxLeaveAmtPerPeriod{
  display: flex;
  gap:30px;
}
</style>
