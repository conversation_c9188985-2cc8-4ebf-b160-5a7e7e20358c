import {
  LeaveUnit,
  SpecialDateStatus,
  SpecialDateType,
} from '@/consts/special-date';
import { namespaceT } from '@/helps/namespace-t';

const t = namespaceT('specialDate');

export const getLeaveUnitText = (unit: LeaveUnit) => {
  const map = new Map(
    Object.values(LeaveUnit).map((item) => [item, t(`leaveUnit.${item}`)]),
  );

  if (map.has(unit)) {
    return map.get(unit);
  }
  return '';
};

export const getSpecialDateStatusText = (status: SpecialDateStatus) => {
  const map = new Map(
    Object.values(SpecialDateStatus).map((item) => [item, t(`status.${item}`)]),
  );

  if (map.has(status)) {
    return map.get(status);
  }
  return '';
};

export const getSpecialDateTypeText = (type: SpecialDateType):string => {
  const map = new Map(
    Object.values(SpecialDateType).map((item) => [item, t(`type.${item}`)]),
  );

  if (map.has(type)) {
    return map.get(type);
  }
  return '';
};

export const getLeaveUnitOptions = () => {
  return Object.values(LeaveUnit).map((item) => ({
    label: getLeaveUnitText(item),
    value: item,
  }));
};

export const getSpecialDateStatusOptions = () => {
  return Object.values(SpecialDateStatus).map((item:SpecialDateStatus) => {
    return ({
      label: getSpecialDateStatusText(item),
      value: item,
    });
  });
};

export const getSpecialDateTypeOptions = () => {
  return Object.values(SpecialDateType).map((item) => ({
    label: getSpecialDateTypeText(item),
    value: item,
  }));
};
