import { LeaveUnit, SpecialDateStatus, SpecialDateType } from '@/consts/special-date';

export default {
  title: {
    add: '新增特殊日期',
    modify: '修改特殊日期',

  },
  columns: {
    date: '日期',
    name: '名称',
    type: '类型',
    duration: '时长',
    status: '状态',
    operation: '操作',
    leaveUnit: '休假单位',
    time: '时间',
  },

  content: {
    day: '{day}天',
    hour: '{hour}小时',
    delete: '确定删除此日期吗？',
  },


  actions: {
    add: '新增',
    enable: '启用',
    stop: '禁用',
    modify: '修改',
    delete: '删除',
    cancel: '取消',
    save: '保存',
  },

  hint: {
    disabledDeleteDataForEnabled: '当前日期正在启用，不可删除',
  },


  type: {
    [SpecialDateType.HOLIDAY]: '假期',
    [SpecialDateType.WORK_DAY]: '非休息日和非节假日',
  },

  leaveUnit: {
    [LeaveUnit.DAY]: '天',
    [LeaveUnit.HOUR]: '小时',
  },

  status: {
    [SpecialDateStatus.DISABLE]: '禁用',
    [SpecialDateStatus.ENABLE]: '启用',
  },


};
