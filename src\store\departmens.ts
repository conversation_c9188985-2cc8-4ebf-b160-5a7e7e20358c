import { defineStore } from 'pinia';

import { DepartmentsApi } from '@/api/departments';


export function createDepartmentsStore() {
  const initState = {
    data: null,
    loaded: false,
    loading: false,
  };

  const getters = {
    hasData() {
      return this.data && this.data.length > 0;
    },
  };

  const actions = {
    async loadDataIfNeeded() {
      if (this.loading || this.loaded) {
        return;
      }

      this.loading = true;
      try {
        const api = new DepartmentsApi();
        api.params = {
          limit: -1,
          page: 1,
        };
        const data = await api.send();
        this.data = data;
        this.loaded = true;
      } catch (error) {
        this.data = null;
        throw error;
      } finally {
        this.loading = false;
      }
    },
  };

  return defineStore('DepartmentsStore', {
    state: () => initState,
    actions,
    getters,
  })();
}
