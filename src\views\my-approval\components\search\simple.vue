<script setup lang="ts">
import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectApprovalStatus from '@/components/biz/select/approval-status.vue';
import SelectApprovalType from '@/components/biz/select/approval-type.vue';

import { MaxLength } from '@/consts/max-length';
import { namespaceT } from '@/helps/namespace-t';
import { UseQueryTableReturn } from '@/uses/query-table';


const emit = defineEmits<{
  'on-search': [],
}>();

const qt = defineModel<UseQueryTableReturn>();


const t = namespaceT('myApproval');
const tc = namespaceT('common');

</script>

<template>
  <WrapperSearchSimple
    :show="qt.shown.value"
    advanced
    @show-search-advanced="qt.showAdvancedSearch"
  >
    <!--  状态 -->
    <PairLabelItem :label="t('search.label.approvalStatus')">
      <SelectApprovalStatus
        v-model="qt.simpleSearchModel.status"
        clearable
        class="w-200"
        :placeholder="tc('placeholder.all')"
        @on-change="emit('on-search')"
      />
    </PairLabelItem>

    <!--  审批类型 -->
    <PairLabelItem :label="t('search.label.approvalType')">
      <SelectApprovalType
        v-model="qt.simpleSearchModel.applyType"
        clearable
        class="w-200"
        :placeholder="tc('placeholder.all')"
        @on-change="emit('on-search')"
      />
    </PairLabelItem>

    <!-- 搜索框 -->
    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="qt.simpleSearchModel.keyword"
        clearable
        class="w-400"
        :placeholder="t('search.placeholder.keyword')"
        :max-length="MaxLength.INPUT_MAX_LENGTH"
        @on-clear="emit('on-search')"
        @on-search="emit('on-search')"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>
