import eventBus from '@/utils/event-bus';
import { namespaceT } from '@/helps/namespace-t';


export function errorInterceptor(error) {
  // 如果响应为401状态码错误，则通知相关事件
  if (error.response?.status === 401) {
    eventBus.emit('userUnauthorizedError');
    throw error;
  }

  // 用户AccessToken失效
  if (['F_GET_TOKEN_FAILED', 'ACCESS_CHECK'].includes(error.code)) {
    eventBus.emit('invaildAccessTokenError');
    throw error;
  }

  // 用户RefershToken失效
  if (error.code === 'F_REFRESH_TOKEN_FAILED'
    || (error.code === 'INVALID' && error.response?.data?.errorList?.INVALID === 'refreshToken')) {
    eventBus.emit('invaildRefreshTokenError');
    throw error;
  }

  if (['ACCOUNT_DISABLE', 'ACCOUNT_EXCEPTION'].includes(error.code)) {
    eventBus.emit('catchAccountError', error.code);
    throw error;
  }

  // 超时
  if (error.response?.status === 408
    || (error.code === 'ECONNABORTED' && /timeout of \d+ms exceeded$/.test(error.message))) {
    const t = namespaceT('common.error');
    const err = new Error(t('timeoutError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // 未知错误
  if (error.code === 'OTHER') {
    const t = namespaceT('common.error');
    const err = new Error(t('unknowError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  throw error;
}
