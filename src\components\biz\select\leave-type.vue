
<script setup lang="ts">
import { computed, onMounted } from 'vue';

import { createLeaveTypesStore } from '@/store/leave-type';


const store = createLeaveTypesStore();

const options = computed(() => {
  if (!(store.data && store.data.total)) {
    return [];
  }

  const list = store.data.data.map((item) => ({
    label: item.name,
    value: item.id,
  }));
  return list;
});

onMounted(() => {
  store.loadDataIfNeeded();
});

</script>

<template>
  <Select
    class="pima-select"
  >
    <Option
      v-for="item in options"
      :key="item.value"
      :value="item.value"
      :label="item.label"
    />
  </Select>
</template>
