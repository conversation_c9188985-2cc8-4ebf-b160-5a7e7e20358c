import { EnableStatus, IsNeedAttachment } from '@/consts/leave-type';

export default {
  title: {
    detail: '假期设置',
    baseInfo: '基本信息',
    rule: '请假时长核算规则',
    other: '其他',
  },
  search: {
    label: {
      college: '学院',
    },

    placeholder: {
      keyword: '可输入假期名称、学院名称查询',
    },
  },

  columns: {
    typeName: '类型名称',
    leaveName: '假期名称',
    maxLeaveDurationForSignal: '单次最大请假时长',
    maxCumulativeLeaveDuration: '累计最多请假时长',
    scope: '适用范围',
    isEnabled: '是否启用',
    operation: '操作',
  },

  label: {
    typeName: '类型名称',
    name: '假期名称',
    code: '假期编码',
    scope: '适用范围',

    timeLimitForLeave: '可请假时间范围',
    maxDuration: {
      label: '最大请假时长',
      content1: '学生单次最多可请',
      content2: '天假期',
    },
    cumulativeDuration: {
      label: '累计请假时长',
      content1: '学生累计最多可请',
      content2: '天假期',
    },

    attachmentRequirements: '附件要求',
    format: '附件格式',
    LimitSize: {
      label: '附件大小限制',
      content1: '上传附件不可大于',
      content2: 'MB',
    },
    leaveInstructions: '休假说明',

    whetherToEnableTheHoliday: '是否启用该假期',
    leave: '假期{index}',
  },


  content: {
    day: '{day}天',
    notLimit: '不限制',
    to: '至',
    batchCopy: '您已选择{count}条假期，请选择将假期复制到',
    disenable: '(停用)',
  },

  actions: {
    copy: '复制',
    modify: '修改',
    delete: '删除',
    batchCopy: '批量复制',
    addLeave: '+新增假期',
    addType: '+新增类型',
    leaveConfig: '假期配置',
  },

  error: {
    mustBeGreaterThanZero: '请输入大于0的数值',
  },

  hint: {
    successCopy: '复制成功',
    mustBeSelectDept: '请选择适用范围',

  },

  status: {
    [EnableStatus.ENABLE]: '是',
    [EnableStatus.DISABLE]: '否',
  },

  isNeedAttachment: {
    [IsNeedAttachment.YES]: '需上传附件',
    [IsNeedAttachment.NO]: '无需上传附件',
  },

  modalAddType: {
    title: '新增类型',
  },

  modalModifyType: {
    title: '修改类型',
  },


};
