import { Message } from 'view-ui-plus';


/**
 * // openToastLoading usage:
 *
 * const close = openToastLoading('loading-text', 1500)
 * ...
 * doSomething() {
 *  ...
 *  ...
 *  close()
 *  // or
 *  close().then(() => {
 *    console.log('loading has been closed')
 *  })
 * }
 */
export function openToastLoading(text, minAliveTime) {
  const destroy = Message.loading({
    content: text,
    duration: 0,
  });

  let executed = false;
  let closePromise = null;
  const close = () => {
    if (!executed) {
      executed = true;

      closePromise = new Promise((resolve) => {
        setTimeout(() => {
          destroy();
          resolve();
        }, minAliveTime);
      });
    }

    return closePromise;
  };

  return close;
}

export function openToastError(content) {
  Message.error({ content });
}

export function openToastSuccess(content, duration = 1.5) {
  Message.success({ content, duration });
}
