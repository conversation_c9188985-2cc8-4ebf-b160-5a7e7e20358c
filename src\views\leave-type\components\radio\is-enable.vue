
<script setup lang="ts">
import { computed } from 'vue';

import { getEnableStatusOptions } from '@/utils/leave-type';

const options = computed(() => getEnableStatusOptions());

</script>

<template>
  <RadioGroup
    class="pima-radio-group-vertical"
    v-bind="$attrs"
  >
    <Radio
      v-for="item in options"
      :key="item.value"
      :label="item.value"
    >
      {{ item.label }}
    </Radio>
  </RadioGroup>
</template>
