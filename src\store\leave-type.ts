import { defineStore } from 'pinia';

import { LeaveTypeListAPi } from '@/api/leave-query/leave-type';


export function createLeaveTypesStore() {
  const initState = {
    data: null,
    loaded: false,
    loading: false,
  };

  const getters = {
    hasData() {
      return this.data && this.data.length > 0;
    },
  };

  const actions = {
    async loadDataIfNeeded() {
      if (this.loading || this.loaded) {
        return;
      }

      this.loading = true;
      try {
        const api = new LeaveTypeListAPi({});
        api.params = {
          limit: -1,
          page: 1,
          isSearchAllChild: true,
        };
        const data = await api.send();
        this.data = data;
        this.loaded = true;
      } catch (error) {
        this.data = null;
        throw error;
      } finally {
        this.loading = false;
      }
    },
  };

  return defineStore('LeaveTypesStore', {
    state: () => initState,
    actions,
    getters,
  })();
}
