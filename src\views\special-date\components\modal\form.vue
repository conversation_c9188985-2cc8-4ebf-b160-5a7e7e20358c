<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue';

import ModalForm from '@/components/common/modal-form.vue';
// eslint-disable-next-line import/order
import FormDetail from './form-content.vue';


import { AddApi } from '@/api/special-date/add';
import { ModifyApi } from '@/api/special-date/modify';
import { GetDetailApi } from '@/api/special-date/detail';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { handleAddData, handleModifyData } from '../../helps/handle-api-data';
import { createModalFormRules } from '../../helps/rules';
import { ModalType, FormType, OpenModalType, useFormModal, useTimeRange } from '../../helps/data';


const emit = defineEmits<{
  'save-success': []
}>();

const props = withDefaults(defineProps<{
  id?:number
  type:OpenModalType
}>(), {
  id: null,
  type: OpenModalType.ADD,
});


const formModal = ref<ModalType<FormType>>(useFormModal());
const t = namespaceT('specialDate');
const th = namespaceT('common.hint');
const formRef = ref();
const loading = ref(false);

const title = computed(() => {
  return props.type === OpenModalType.ADD ? t('title.add') : t('title.modify');
});

const isModify = computed(() => props.type === OpenModalType.MODIFY);
const onVisibleChange = () => {
  formModal.value.visible = !formModal.value.visible;
};

const changeLoadingStatus = () => {
  loading.value = !loading.value;
};


const onCancel = () => {
  formModal.value.visible = false;
};


const onAddDetail = async () => {
  try {
    changeLoadingStatus();
    const api = new AddApi({});
    api.data = handleAddData(formModal.value.model);
    await api.send();
    openToastSuccess(th('dataSaved'));
    onVisibleChange();
    emit('save-success');
  } catch (error) {
    openToastError(error.message);
  } finally {
    changeLoadingStatus();
  }
};

const onModifyDetail = async () => {
  try {
    changeLoadingStatus();
    const api = new ModifyApi({ id: props.id });
    api.data = handleModifyData(formModal.value.model);
    await api.send();
    openToastSuccess(th('dataSaved'));
    onVisibleChange();
    emit('save-success');
  } catch (error) {
    openToastError(error.message);
  } finally {
    changeLoadingStatus();
  }
};

const getDetail = async () => {
  try {
    const api = new GetDetailApi({ id: props.id });
    const res = await api.send();
    Object.assign(formModal.value.model, res);
  } catch (error) {
    openToastError(error.message);
  }
};


const onValidate = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (props.type === OpenModalType.ADD) {
        onAddDetail();
      } else {
        onModifyDetail();
      }
    }
  });
};


const onAdd = () => {
  formModal.value.model.time.push(useTimeRange());
};

const onRemove = (id:string) => {
  formModal.value.model.time = formModal.value.model.time.filter((item) => item.id !== id);
};


watch(() => formModal.value.visible, async (val) => {
  if (val) {
    Object.assign(formModal.value.model, {
      time: [useTimeRange()],
    });
    await nextTick();
    formRef.value.resetFields();

    if (props.type === OpenModalType.MODIFY) {
      getDetail();
    }
  }
});

defineExpose({
  Close: onVisibleChange,
  Open: onVisibleChange,
});

</script>

<template>
  <ModalForm
    v-model="formModal.visible"
    :title="title"
    @on-cancel="onCancel"
  >
    <template #content>
      <Form
        ref="formRef"
        :model="formModal.model"
        :rules="createModalFormRules()"
        :label-width="100"
        label-colon
      >
        <FormDetail
          v-model="formModal.model"
          :is-modify="isModify"
          @on-add="onAdd"
          @on-remove="onRemove"
        />
      </Form>
    </template>
    <template #button>
      <Button
        class="pima-btn"
        :disabled="loading"
        @click="onCancel"
      >
        {{ t('actions.cancel') }}
      </Button>

      <Button
        class="pima-btn"
        type="primary"
        :loading="loading"
        @click="onValidate"
      >
        {{ t('actions.save') }}
      </Button>
    </template>
  </ModalForm>
</template>
