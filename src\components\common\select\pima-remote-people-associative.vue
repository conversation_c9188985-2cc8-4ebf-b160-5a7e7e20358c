
<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

// @ts-expect-error: Cannot find module 'pimaRemoteUI/PimaAppHeader' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaSelectPeopleAssociative = defineAsyncComponent(() => import('pimaRemoteUI/PimaSelectPeopleAssociative'));

</script>

<template>
  <PimaSelectPeopleAssociative
    class="pima-select-people-associative"
    v-bind="$attrs"
  />
</template>

<style lang="less" scoped>
.pima-select-people-associative{
  background-color: #fff;
}
</style>
