import type { I18n } from 'vue-i18n';
import { SUPPORT_LOCALES } from '@/config/locale';
import {
  LOCAL_BDC_CORE_API_BASE_URL,
  LOCAL_BDC_ARCH_API_BASE_URL,
  LOCAL_BDC_IMPORT_API_BASE_URL,
  LOCAL_BDC_EXPORT_API_BASE_URL } from '@/config/api';


export const PimaRemoteUIPlugin = {
  install(Vue, options: {
    i18n: I18n
  }) {
    if (!options.i18n) {
      throw new Error('PimaRemoteUIPlugin need i18n option');
    }

    if (typeof document !== 'undefined') {
      // @ts-expect-error: Cannot find module 'pimaRemoteUI/init' or its corresponding type declarations.
      // eslint-disable-next-line import/no-unresolved
      import('pimaRemoteUI/init').then(({ init }) => {
        init({
          apiBaseUrls: {
            bdcCoreApiBaseUrl: LOCAL_BDC_CORE_API_BASE_URL,
            bdcArchApiBaseUrl: LOCAL_BDC_ARCH_API_BASE_URL,
            bdcImportApiBaseUrl: LOCAL_BDC_IMPORT_API_BASE_URL,
            bdcExportApiBaseUrl: LOCAL_BDC_EXPORT_API_BASE_URL,
          },
          locale: options.i18n.global.locale.value,
          serviceCode: process.env.SERVICE_CODE,
          appHeader: {
            supportLocales: SUPPORT_LOCALES,
          },
        }, true);
      });
    }
  },
};
