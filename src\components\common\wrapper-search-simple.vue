<template>
  <div class="pima-search-simple-wrapper">
    <div
      v-show="show"
      class="left"
    >
      <slot />

      <!-- 高级搜索按钮 -->
      <template v-if="advanced">
        <Button
          type="text"
          class="ml-15 pima-btn"
          @click="onShow"
        >
          {{ $t('common.action.advancedSearch') }}
        </Button>
      </template>
    </div>

    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<script lang='ts' setup>

// 定义props
defineProps({
  show: {
    type: Boolean,
    default: true,
  },
  advanced: {
    type: Boolean,
    default: false,
  },
});

// 定义emits
const emit = defineEmits(['show-search-advanced']);

// 方法定义
const onShow = () => {
  emit('show-search-advanced');
};

</script>

<style lang="less" scoped>

.right{
  margin-left:auto ;
}
</style>
