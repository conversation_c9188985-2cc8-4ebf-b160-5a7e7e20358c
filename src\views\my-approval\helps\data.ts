import { namespaceT } from '@/helps/namespace-t';

import { LeaveReceiverVO } from '../type/list';

export const createSimpleSearchModel = ():SearchModel => ({
  status: null,
  applyType: null,
  keyword: '',
});
export const createAdvanceSearchModel = ():AdvanceSearchModel => ({
  deptId: null,
  leaveTypeId: null,
  status: null,
  sn: null,
  applyUserName: null,
  startDate: null,
  endDate: null,
  applyType: null,
});

export const createApprovalModal = ():ApprovalModal => ({
  remark: null,
});


export const useApprovalModal = ():UseApprovalModalType => {
  return {
    model: createApprovalModal(),
    visible: false,
  };
};

export const createModalRules = () => {
  const t = namespaceT('myApproval.modal.hints');
  return {
    remark: [
      { required: true, message: t('remark'), trigger: 'change' },
    ],
  };
};


export interface SearchModel {
  status: string;
  applyType: string;
  keyword: string;
}
export interface AdvanceSearchModel {
  deptId: string;
  leaveTypeId: string;
  status: string;
  sn: string;
  applyUserName: string;
  startDate: string;
  endDate: string;
  applyType: string;
}

export type TableRowData = LeaveReceiverVO;

export interface ApprovalModal {
  remark:string
}


export interface UseApprovalModalType {
  model: ApprovalModal,
  visible: boolean,
}
