import _ from 'lodash';
import { endOfDay } from 'date-fns';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { AdvanceSearchModel } from './data';

export const handleListData = (data:AdvanceSearchModel):AdvanceSearchModel => {
  const cloneData = _.cloneDeep(data);
  const t = namespaceT('dateFormat');
  if (cloneData.startDate && cloneData.endDate) {
    cloneData.startDate = dateFormatSTZ(cloneData.startDate, t('fullDateTime'));
    cloneData.endDate = dateFormatSTZ(endOfDay(cloneData.endDate as unknown as Date), t('fullDateTime'));
  }
  return cloneData;
};
