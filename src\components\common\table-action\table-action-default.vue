<template>
  <div>
    <Button
      v-for="(item, index) of actions"
      :key="index"
      type="text"
      class="pima-btn"
      :disabled="item.disabled"
      @click="onClick(item.triggerEvent)"
    >
      {{ item.label }}
    </Button>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'TableActionDefault',

  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  emits: ['trigger'],

  setup(props, { emit }) {
    function onClick(triggerEvent) {
      emit('trigger', triggerEvent);
    }

    return {
      onClick,
    };
  },
});
</script>
