<script setup lang='ts'>
import ButtonGoBack from '@/components/common/button-go-back.vue';


defineProps<{
  title?: string

  goBack?: boolean
}>();

const emit = defineEmits(['go-back']);
</script>


<template>
  <div class="pima-title-bar">
    <template v-if="goBack">
      <ButtonGoBack
        class="mr-15"
        @click="emit('go-back')"
      />
    </template>

    <span
      v-if="title"
      class="title"
    >{{ title }}</span>

    <slot />

    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>
