import { EnableStatus, IsNeedAttachment } from '@/consts/leave-type';

export default {
  title: {
    detail: 'Holiday settings',
    baseInfo: 'Basic information',
    rule: 'Accounting rules for leave duration',
    other: 'Other',
  },
  search: {
    label: {
      college: 'college',
    },
    placeholder: {
      keyword: 'You can enter the holiday name and college name to search',
    },
  },

  columns: {
    name: 'Holiday name',
    maxLeaveDurationForSignal: 'The maximum duration of a single leave request',
    maxCumulativeLeaveDuration: 'Accumulated maximum leave duration',
    scope: 'Applicable scope',
    isEnabled: 'Is it enabled',
    operation: 'operation',
  },

  label: {
    name: 'Holiday name',
    code: 'holiday code',
    scope: 'Applicable scope',
    timeLimitForLeave: 'The time range for which leave can be requested',
    maxDuration: {
      label: 'Maximum leave duration',
      content1: 'Students can only request a maximum of at a time',
      content2: 'Day holiday',
    },
    cumulativeDuration: {
      label: 'Accumulated leave duration',
      content1: 'Students can apply up to a maximum of',
      content2: 'Day holiday',
    },
    attachmentRequirements: 'Attachment requirements',
    format: 'Attachment format',
    LimitSize: {
      label: 'Attachment size limit',
      content1: 'The uploaded attachment cannot be larger than',
      content2: 'MB',
    },
    leaveInstructions: 'Leave instructions',
    whetherToEnableTheHoliday: 'Do you want to enable this holiday',
  },

  content: {
    day: '{day} days',
    notLimit: 'Unrestricted',
    to: 'To',
    batchCopy: 'You have selected {count} holidays. Please choose to copy the holidays to',
  },

  actions: {
    copy: 'Copy',
    modify: 'modify',
    delete: 'delete',
    batchCopy: 'Batch copying',
    addLeave: '+Add holidays',
  },

  error: {
    mustBeGreaterThanZero: 'Please enter a value greater than 0',
  },

  hint: {
    successCopy: 'Copy successful',
    mustBeSelectDept: 'Please select the applicable scope',
  },

  status: {
    [EnableStatus.ENABLE]: 'Yes',
    [EnableStatus.DISABLE]: 'No',
  },

  isNeedAttachment: {
    [IsNeedAttachment.YES]: 'An attachment needs to be uploaded',
    [IsNeedAttachment.NO]: 'No need to upload attachments',
  },
};
