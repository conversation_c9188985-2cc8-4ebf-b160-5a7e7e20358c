<template>
  <div class="pima-paginator-wrapper">
    <div class="paginator">
      <template v-if="!simple">
        <span class="total">
          {{ t('pageTotal') }}
          <span class="count">{{ total }}</span>
          {{ t('record') }}
        </span>
      </template>

      <Page
        :model-value="pageIndex"
        :show-sizer="true"
        :total="total"
        :page-size="pageSize"
        :page-size-opts="pageSizeOptions"
        :prev-text="t('prevPage')"
        :next-text="t('nextPage')"
        @on-change="onPageIndexChange"
        @on-page-size-change="onPageSizeChange"
      />

      <template v-if="!simple">
        <span class="jump-to">{{ t('jumpTo') }}</span>

        <InputNumber
          v-model="jumpPage"
          class="paget-index-input"
          :precision="0"
          :min="minPage"
          :max="pageCount"
          @on-blur="onBlur"
        />

        <span class="page">{{ t('page') }}</span>

        <Button
          class="jump-button"
          type="primary"
          @click="onPageIndexChange(jumpPage)"
        >
          {{ t('jump') }}
        </Button>
      </template>
    </div>
  </div>
</template>


<script lang='ts'>
import { computed, defineComponent, onBeforeUnmount, ref, toRefs, unref, watch } from 'vue';
import _ from 'lodash';

import { namespaceT } from '@/helps/namespace-t';


export default defineComponent({
  name: 'PaginatorComp',

  props: {
    total: {
      type: Number,
      default: 0,
    },

    pageIndex: {
      type: Number,
      default: 1,
    },

    pageSize: {
      type: Number,
      default: 10,
    },

    pageSizeOptions: {
      type: Array,
      default: () => [10, 20, 40, 80],
    },

    simple: Boolean,
  },
  emits: ['update:pageIndex', 'page-index-change', 'update:pageSize', 'page-size-change', 'change'],

  setup(props, { emit }) {
    const { pageIndex, total, pageSize } = toRefs(props);

    const jumpPage = ref(unref(pageIndex));
    const minPage = ref(1);

    // 当 pageIndex 不为 1 pageSize 发生变化时
    // iview 的 page 组件会连续发送 page size change 和 page index change
    // 通过 debounce 将这两个事件"合并"成一个事件
    function triggerEmitChange() {
      emit('change');
    }

    const emitChange = _.debounce(triggerEmitChange, 10);

    const pageCount = computed(() => Math.ceil(total.value / pageSize.value));

    function onPageIndexChange(index) {
      if (index > unref(pageCount)) {
        emit('update:pageIndex', unref(pageCount));
        emit('page-index-change', unref(pageCount));
        emitChange();
      } else if (index < 1) {
        emit('update:pageIndex', 1);
        emit('page-index-change', 1);
        emitChange();
      } else {
        emit('update:pageIndex', index);
        emit('page-index-change', index);
        emitChange();
      }
    }

    function onPageSizeChange(size) {
      emit('update:pageSize', size);
      emit('page-size-change', size);
      emitChange();
    }

    function onBlur() {
      if ([undefined, null, ''].includes(String(unref(jumpPage)))) {
        jumpPage.value = unref(minPage);
      }
    }

    const watchStop = watch(pageIndex, (val) => {
      jumpPage.value = val;
    });

    onBeforeUnmount(() => {
      watchStop();
    });

    return {
      jumpPage,
      minPage,
      emitChange,
      pageCount,
      onPageIndexChange,
      onPageSizeChange,
      onBlur,
      t: namespaceT('paginator'),
    };
  },
});
</script>
