import { LeaveUnit, SpecialDateStatus, SpecialDateType } from '@/consts/special-date';

export default {
  title: {
    add: 'Add a special date',
    modify: 'Modify special dates',
  },
  columns: {
    date: 'Date',
    name: 'Name',
    type: 'Type',
    duration: 'Duration',
    status: 'Status',
    operation: 'Operation',
    leaveUnit: 'Vacation unit',
    time: 'Time',
  },

  content: {
    day: '{day} days',
    hour: '{hour} hours',
    delete: 'Are you sure to delete this date?',
  },

  actions: {
    add: 'New',
    enable: 'Enable',
    stop: 'disabled',
    modify: 'modify',
    delete: 'delete',
    cancel: 'cancel',
    save: 'Save',
  },

  hint: {
    disabledDeleteDataForEnabled: 'The current date is being enabled and cannot be deleted',
  },

  type: {
    [SpecialDateType.HOLIDAY]: 'vacation',
    [SpecialDateType.WORK_DAY]: 'Non rest days and non holidays',
  },

  leaveUnit: {
    [LeaveUnit.DAY]: 'Day',
    [LeaveUnit.HOUR]: 'hours',
  },

  status: {
    [SpecialDateStatus.DISABLE]: 'disabled',
    [SpecialDateStatus.ENABLE]: 'enabled',
  },
};
