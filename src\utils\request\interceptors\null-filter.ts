import _ from 'lodash';


export function interceptorNullFilter(config) {
  const paramsWithoutNullKeys = Object.keys(config.params).filter((key) => config.params[key] !== null);
  const params = _.pick(config.params, paramsWithoutNullKeys);
  Object.assign(config, {
    params,
  });

  if (_.isPlainObject(config.data)) {
    const dataWithoutNullKeys = Object.keys(config.data).filter((key) => config.data[key] !== null);
    const data = _.pick(config.data, dataWithoutNullKeys);
    Object.assign(config, {
      data,
    });
  }


  console.log('%c [ config ]-20', 'font-size:13px; background:#4a9d24; color:#8ee168;', config);
  return config;
}
