
<script setup lang="ts">
import PimaInput from '@/components/common/pima-input.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';

import { CommonDepartmentsParams } from '@/consts/common-departments';
import { MaxLength } from '@/consts/max-length';
import { namespaceT } from '@/helps/namespace-t';

import { FormModel } from '../../helps/data';


const emit = defineEmits<{
  'scope-change':[]
}>();

const t = namespaceT('leaveType');
const model = defineModel<FormModel>();


</script>

<template>
  <!-- 假期名称 -->
  <FormItem
    :label="t('label.name')"
    prop="name"
  >
    <PimaInput
      v-model.trim="model.name"
      class="w-350"
      clearable
      :max-length="MaxLength.INPUT_MAX_LENGTH"
    />
  </FormItem>

  <!-- 假期编码 -->
  <FormItem
    :label="t('label.code')"
    prop="code"
  >
    <PimaInput
      v-model.trim="model.code"
      class="w-350"
      disabled
    />
  </FormItem>

  <!-- 适用范围 -->
  <FormItem
    :label="t('label.scope')"
    prop="deptIds"
  >
    <SelectPimaRemoteDepartment
      v-model="model.deptIds"
      multiple
      is-concat-parent-dept-name
      filterable
      :width="350"
      :parent-code="CommonDepartmentsParams.Student"
      @on-change="emit('scope-change')"
    />
  </FormItem>
</template>

<style lang="less" scoped>
:deep(.ivu-input.ivu-input-default.ivu-input-disabled){
  cursor: not-allowed;
}
</style>
