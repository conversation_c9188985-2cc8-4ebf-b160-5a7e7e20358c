<!-- eslint-disable import/order -->
<script setup lang="ts">
import { onMounted } from 'vue';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import Search from '../components/search.vue';
import QueryTable from '../components/query-table.vue';

import { TableListApi } from '@/api/my-approval/list';
import { openToastError } from '@/helps/toast';
import { useQueryTable } from '@/uses/query-table';

import { handleListData } from '../helps/handle-api-data';
import { createSimpleSearchModel, createAdvanceSearchModel } from '../helps/data';


async function loadData(option) {
  try {
    const params = option || {};

    const api = new TableListApi({});
    api.params = handleListData(params);
    const res = await api.send();
    return res;
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
}

const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSimpleSearchModel(),
  advancedSearchModel: createAdvanceSearchModel(),
});


onMounted(() => {
  qt.load();
});

</script>


<template>
  <Search
    v-model="qt"
    @on-search="qt.search"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      :total="qt.table.total"
      :page="qt.page"
      @on-reload="qt.load"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>
</template>
