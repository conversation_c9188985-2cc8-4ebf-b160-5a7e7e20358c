
/**
 * LeaveVO，请假VO
 */
export interface LeaveVO {
  /**
   * 请假查询的 审批状态 枚举：LEAVE_APPROVAL_STATUS
   */
  approvalStatus?: string;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 请假天数
   */
  days?: number;
  /**
   * 申请部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 请假结束时间
   */
  endDate?: Date;
  /**
   * 请假结束值
   */
  endValue?: string;
  /**
   * 请假小时
   */
  hours?: number;
  /**
   * id
   */
  id?: number;
  /**
   * 是否可以撤销申请
   */
  isCancel?: boolean;
  /**
   * 是否可以重新编辑
   */
  isEdit?: boolean;
  /**
   * 请假类型ID
   */
  leaveTypeId?: number;
  /**
   * 请假类型名称
   */
  leaveTypeName?: string;
  /**
   * 专业
   */
  major?: string;
  /**
   * 请假原因
   */
  reason?: string;
  /**
   * 申请编号
   */
  sn?: string;
  /**
   * 请假开始时间
   */
  startDate?: Date;
  /**
   * 请假开始值
   */
  startValue?: string;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新人名称
   */
  updateUserName?: string;
  /**
   * 申请人英文名
   */
  userEnName?: string;
  /**
   * 申请人姓名
   */
  userName?: string;
  /**
   * 申请人工号
   */
  userNo?: string;

  /** 开始时间段 */
  startValue?:string;
  /** 结束时间段 */
  endValue?:string;
  /** 计算接口返回的请假时长 */
  leaveDays?:number;
  /** 请假时间 是否超过一天 */
  isGtOneDay?: boolean;

  [property: string]: any;
}
