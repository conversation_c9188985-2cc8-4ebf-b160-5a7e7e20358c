<script setup lang="ts">
import { getCurrentInstance, ref } from 'vue';
import { useRouter } from 'vue-router';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import ApprovalModal from './modal/approval.vue';

import { RouterName as RN } from '@/config/router';
import { ApprovalResult } from '@/consts/approval-result';
import { Status } from '@/consts/status';
import { namespaceT } from '@/helps/namespace-t';
import { dateFormatSTZ } from '@/helps/date';
import { push } from '@/helps/navigation';
import { handleRowDateText } from '@/helps/leave-time-text';
import { getApprovalStatusText, getApprovalStatusColor } from '@/helps/get-status-text';
import { getEmptyText } from '@/utils/empty-text';
import { getApprovalTypeText } from '@/utils/get-text-or-options';

import { createColumns } from './columns';
import { TableRowData, useApprovalModal } from '../helps/data';
import { ApprovalType } from '@/consts/approval-type';

withDefaults(defineProps<{
  data: TableRowData[],
  total:number,
  loading: boolean,
}>(), {
  data: null,
  loading: false,
  total: 0,
});

const emit = defineEmits<{
  'on-reload': []
}>();

const vm = getCurrentInstance();
const td = namespaceT('dateFormat');
const t = namespaceT('myApproval');
const router = useRouter();

const approvalModalInfo = ref({
  id: null,
  type: ApprovalResult.AGREE,
  leaveType: ApprovalType.LEAVE,
});
const tableRef = ref();

const approvalModal = ref(useApprovalModal());

const setApprovalModalInfo = ({
  id = null,
  type = ApprovalResult.AGREE,
  leaveType = ApprovalType.LEAVE,
}) => {
  Object.assign(
    approvalModalInfo.value,
    {
      id,
      type,
      leaveType,
    },
  );
  approvalModal.value.visible = true;
};

const onAgree = (row:TableRowData) => {
  setApprovalModalInfo({ id: row.id, type: ApprovalResult.AGREE, leaveType: row.applyType });
};

const onReject = (row:TableRowData) => {
  setApprovalModalInfo({ id: row.id, type: ApprovalResult.REJECT });
};


const canApprove = (status:Status) => {
  const canApproveStatusArray = [Status.PENDING, Status.PROCESSING];

  return canApproveStatusArray.includes(status);
};

const actions = [
  {
    label: t('actions.agree'),
    triggerEvent: onAgree,
    can: vm.proxy.$can((P) => P.MyApproval.Approval),
  },
  {
    label: t('actions.reject'),
    triggerEvent: onReject,
    can: vm.proxy.$can((P) => P.MyApproval.Approval),
  },
];

const onSuccess = () => {
  approvalModal.value.visible = false;
  emit('on-reload');
};

const toDetailPage = (row:TableRowData) => {
  push(router, {
    name: RN.ApprovalDetail,
    params: {
      id: row.id,
    },
  });
};


</script>


<template>
  <CTable
    ref="tableRef"
    v-bind="$attrs"
    :columns="createColumns()"
    :data="data"
    :loading="loading"
  >
    <!-- 申请单号 -->
    <template #applyOrder="{row}">
      <a @click.prevent="toDetailPage(row)">{{ row.sn }}</a>
    </template>

    <!-- 姓名/学号-->
    <template #nameOrStudId="{ row }">
      <p>{{ getEmptyText(row.userName) }}</p>
      <p>{{ getEmptyText(row.userNo) }}</p>
    </template>

    <!-- 审批类型 -->
    <template #applyType="{row}">
      {{ getApprovalTypeText(row.applyType) }}
    </template>

    <!-- 申请时间 -->
    <template #applyTime="{ row }">
      {{ dateFormatSTZ(row.createTime, td('dateTime')) }}
    </template>

    <!-- 请假时间 -->
    <template #leaveTime="{row}">
      {{ handleRowDateText(row) }}
    </template>

    <!-- 请假时长 -->
    <template #days="{row}">
      {{ t('content.days',{days:row.leaveDays}) }}
    </template>

    <!-- 状态 -->
    <template #approvalStatus="{row}">
      <span
        :style="`color:${getApprovalStatusColor(row.status)}`"
      >
        {{ getApprovalStatusText(row.status) }}
        <span />
      </span>
    </template>

    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          v-if="canApprove(row.status)"
          :row-data="row"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>

  <ApprovalModal
    v-model="approvalModal"
    :detail="approvalModalInfo"
    @on-success="onSuccess"
  />
</template>
