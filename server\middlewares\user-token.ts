/* eslint-disable @typescript-eslint/naming-convention */
enum ErrorCode {
  GET_TOKEN_FAILED = 'F_GET_TOKEN_FAILED',
  REFRESH_TOKEN_FAILED = 'F_REFRESH_TOKEN_FAILED',
  LOCK_FAILED = 'F_LOCK_FAILED',
  INVALID_NONCE = 'F_INVALID_NONCE',
  INVALID_SIGNATURE = 'F_INVALID_SIGNATURE',
}

module.exports = (options) => {
  console.log('%c [  ]-12', 'font-size:13px; background:#8591d0; color:#c9d5ff;');
  const { getRedisValue, setRedisValue } = require('./cas/redis')();
  const md5 = require('js-md5');

  const {
    casUserSessionName,
    tokenSessionName,
    redlock,
    redisClient,
    redisTtl,
    apiSalt,
  } = options;
  const config_casUserSessionName = casUserSessionName;
  const config_tokenSessionName = tokenSessionName || 'appToken';
  const config_redisTtl = redisTtl || 60 * 60 * 1; // nonce有效期為1h
  const config_apiSalt = apiSalt || '';

  if (!config_casUserSessionName) {
    throw new Error('casUserSessionName not configured');
  }

  if (!redisClient) {
    throw new Error('redisClient not configured');
  }

  redlock.on('clientError', (err) => {
    // eslint-disable-next-line no-console
    console.error('A redis error has occurred:', err);
  });

  function getNonceKey(nonce) {
    return `nonce:${nonce}`;
  }

  async function isNonceExists(nonce) {
    const nonceKey = getNonceKey(nonce);
    const data = await getRedisValue(redisClient, nonceKey);
    return !!data;
  }

  async function saveNonce(nonce) {
    const nonceKey = getNonceKey(nonce);
    setRedisValue(redisClient, nonceKey, 1, config_redisTtl);
  }

  function isSignatureValid(params) {
    const paramsKeys = Object.keys(params).filter((k) => k !== 'signature');
    let raw = '';
    for (let i = 0; i < paramsKeys.length; i += 1) {
      const key = paramsKeys[i];
      raw += `${key}=${params[key]}`;
    }

    raw += config_apiSalt;
    // eslint-disable-next-line no-console
    console.log('@@config_apiSalt', config_apiSalt);

    return md5(raw) === params.signature;
  }

  async function userTokenMiddleware(req, res) {
    if (!req.query.nonce) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_NONCE,
      });
      return;
    }

    // 校验nonce参数
    const nonceExists = await isNonceExists(req.query.nonce);
    if (nonceExists) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_NONCE,
      });
      return;
    }

    // 保存nonce参数
    saveNonce(req.query.nonce);

    // 校验签名
    if (!isSignatureValid(req.query)) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_SIGNATURE,
      });
      return;
    }

    if (!req.session[config_tokenSessionName]) {
      res.send({
        success: false,
        errorCode: ErrorCode.GET_TOKEN_FAILED,
      });
      return;
    }

    res.send({
      success: true,
      model: {
        accessToken: req.session[config_tokenSessionName].accessToken,
      },
    });
  }

  return userTokenMiddleware;
};
