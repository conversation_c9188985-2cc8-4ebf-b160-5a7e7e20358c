import { reactive, toRefs, type Ref } from 'vue';
import _ from 'lodash';


interface ListState<T> {
  data: T[],
  total: number,
  loading: boolean,
  searching: boolean,
  isErrored: boolean,
  isFinished: boolean,
  isEmpty: boolean,
  page: {
    page: number,
    limit: number,
  },
}

export interface LoadData<T> {
  (options: {
    page: number,
    limit: number,
  }): Promise<{ data: T[], total: number }>;
}

export type UseListConfig<T> = Partial<ListState<T>> & {
  load: LoadData<T>,
};

export type List<T> = {
  [K in keyof ListState<T>]: Ref<ListState<T>[K]>;
} & {
  load: () => Promise<void>,
  refresh: () => Promise<void>,
  insert: () => Promise<void>,
  search: () => Promise<void>,
};

export function useList<T extends object>(options: UseListConfig<T>): List<T> {
  const state: ListState<T> = reactive({
    data: [],
    total: 0,
    loading: false,
    searching: false,
    isErrored: false,
    isFinished: false,
    isEmpty: true,
    page: {
      page: 1,
      limit: 50,
    },
  });

  Object.assign(state, _.pick(options, _.keys(state)));

  async function loadData() {
    state.loading = true;

    try {
      const params = {
        ...state.page,
      };

      const res = await options.load(params);

      return res;
    } catch (error) {
      state.isErrored = true;

      throw error;
    } finally {
      state.loading = false;
    }
  }

  function recalc() {
    state.isEmpty = state.data.length === 0;
    state.isFinished = state.data.length >= state.total;
    state.page.page += 1;
  }

  async function insert() {
    if (state.isFinished || state.loading) {
      return;
    }

    const { data, total } = await loadData();
    state.data.push(...data);
    state.total = total;

    recalc();
  }

  async function refresh() {
    if (state.loading) {
      return;
    }

    state.isErrored = false;
    state.isFinished = false;
    state.page.page = 1;
    state.total = 0;

    const { data, total } = await loadData();
    state.data.splice(0, state.data.length, ...data);
    state.total = total;

    recalc();
  }

  async function load() {
    await refresh();
  }

  async function search() {
    state.searching = true;

    try {
      await refresh();
    } finally {
      state.searching = false;
    }
  }

  return {
    ...toRefs(state),

    refresh,
    load,
    insert,
    search,
  };
}
