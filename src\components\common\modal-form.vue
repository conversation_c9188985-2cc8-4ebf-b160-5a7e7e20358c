<template>
  <PimaModal
    v-model="visibleValue"
    :width="width"
    :mask-closable="false"
    :closable="true"
    :title="title"
    @on-visible-change="onVisibleChange"
  >
    <slot name="content" />

    <template #footer>
      <div class="action">
        <slot name="button" />
      </div>
    </template>
  </PimaModal>
</template>


<script lang="ts" setup>
import PimaModal from './pima-modal.vue';

interface Props {
  width?: number;
  title?: string;
}

withDefaults(defineProps<Props>(), {
  width: 488,
  title: null,
});

const visibleValue = defineModel<boolean>();

const emit = defineEmits<{
  'on-cancel':[],
  'on-approval':[],
  'on-reject':[],
}>();

const onCancel = () => {
  emit('on-cancel');
};


function onVisibleChange(flag: boolean) {
  if (!flag) {
    onCancel();
  }
}

</script>

<style lang="less" scoped>
:deep(.ivu-modal-body) {
  padding: 0;
}

:deep(.ivu-modal-content) {
  border-radius: 4px;
}

.content {
  display: flex;
  align-items: center;

  .action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    .button-primary {
      width: 90px;
      line-height: 32px;
      border: none;
    }
  }
}

</style>
