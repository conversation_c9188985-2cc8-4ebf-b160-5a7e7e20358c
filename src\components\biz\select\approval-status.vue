<script setup lang="ts">
import { onMounted } from 'vue';

import { useDataTagApprovalStatus } from '@/store/data-tag-approval-status';

const store = useDataTagApprovalStatus();


onMounted(() => {
  store.loadDataIfNeeded();
});


</script>


<template>
  <Select
    class="pima-select"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
