<script setup lang="ts">
defineOptions({
  name: 'PickerFormatDate',
});

interface Props {
  disabledDate?: (date: Date) => boolean;
  placeholder?: string;
  clearable?: boolean;
  format?: string;
}

withDefaults(defineProps<Props>(), {
  disabledDate: () => false,
  placeholder: '',
  clearable: true,
  format: 'yyyy/MM/dd',
});
</script>


<template>
  <DatePicker
    class="pima-date-picker picker-format-date"
    transfer-class-name="pima-date-picker picker-format-date"
    v-bind="$attrs"
    :format="format"
    :options="{ disabledDate }"
    :placeholder="placeholder || $t('common.placeholder.yearMonthDay')"
  />
</template>


<style lang="less" scoped>
.picker-format-date {
  font-size: 12px;
}
</style>
