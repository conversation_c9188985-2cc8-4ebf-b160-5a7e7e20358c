<!-- eslint-disable import/order -->
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import _ from 'lodash';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import Search from '../components/search.vue';
import QueryTable from '../components/query-table.vue';
import ModalForm from '../components/modal/form.vue';
import ModalDelete from '../components/modal/delete.vue';

import { TableListApi } from '@/api/special-date/list';
import { ToggleStatusApi } from '@/api/special-date/toggle-status';
import { SpecialDateStatus } from '@/consts/special-date';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { useQueryTable } from '@/uses/query-table';

import { OpenModalType } from '../helps/data';

interface ModalFormDetailType {
  id:number,
  type:OpenModalType
}

interface ModalDeleteInfo {
  visible?:boolean,
  loading?:boolean
  id?:number
}

const th = namespaceT('common.hint');

async function loadData(option) {
  try {
    const params = option || {};

    const api = new TableListApi({});
    api.params = params;
    const res = await api.send();
    const cloneData = _.cloneDeep(res);
    cloneData.data = cloneData.data.map((item) => {
      return {
        ...item,
        isEnable: item.isEnable ? SpecialDateStatus.ENABLE : SpecialDateStatus.DISABLE,
      };
    });
    return cloneData;
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
}

const qt = useQueryTable({
  load: loadData,
});

const modalFormRef = ref();

// 新增/编辑
const modalFormDetail = ref<ModalFormDetailType>({
  id: null,
  type: OpenModalType.ADD,
});

// 删除
const modalDeleteInfo = ref<ModalDeleteInfo>({
  visible: false,
  loading: false,
  id: null,
});

const setModalFormDetail = (detail:ModalFormDetailType) => {
  Object.assign(modalFormDetail.value, detail);
};

const setModalDeleteInfo = (info:ModalDeleteInfo) => {
  Object.assign(modalDeleteInfo.value, info);
};
const onModify = (id:number) => {
  setModalFormDetail({
    id,
    type: OpenModalType.MODIFY,
  });

  modalFormRef.value.Close();
};

const onDelete = async () => {
  try {
    setModalDeleteInfo({
      loading: true,
    });
    const api = new ToggleStatusApi({ id: modalDeleteInfo.value.id });
    api.params = {
      isDel: true,
    };
    await api.send();
    openToastSuccess(th('successfullyDeleted'));
    qt.load();
    setModalDeleteInfo({
      visible: false,
      id: null,
    });
  } catch (error) {
    openToastError(error.message);
  } finally {
    setModalDeleteInfo({
      loading: false,
    });
  }
};

const onAdd = () => {
  setModalFormDetail({
    id: null,
    type: OpenModalType.ADD,
  });

  modalFormRef.value.Open();
};

const onOpenDeleteModal = (id:number) => {
  setModalDeleteInfo({
    visible: true,
    id,
  });
};


onMounted(() => {
  qt.load();
});

</script>


<template>
  <Search
    v-model="qt"
    @on-search="qt.search"
    @on-add="onAdd"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      :page="qt.page"
      @on-reload="qt.load"
      @on-modify="onModify"
      @on-delete="onOpenDeleteModal"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>

  <ModalForm
    :id="modalFormDetail.id"
    ref="modalFormRef"
    :type="modalFormDetail.type"
    @save-success="qt.load"
  />

  <ModalDelete
    v-model:loading="modalDeleteInfo.loading"
    v-model:visible="modalDeleteInfo.visible"
    @on-confirm="onDelete"
  />
</template>
