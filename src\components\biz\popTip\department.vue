<script setup lang="ts">
import { computed } from 'vue';
import NamesList from './names-list.vue';

const props = defineProps<{
  list:string[]
}>();

const isTooMuchName = computed(() => {
  return props.list.length > 5;
});

const namesList = computed(() => {
  if (isTooMuchName.value) {
    const list = props.list.slice(0, 5);
    list.push('...');
    return list;
  }
  return props.list;
});

</script>

<template>
  <Poptip
    v-if="isTooMuchName"
    trigger="hover"
    content="content"
    transfer
  >
    <NamesList :list="namesList" />

    <template #content>
      <NamesList :list="list" />
    </template>
  </Poptip>

  <div
    v-else
    class="names-list"
  >
    <NamesList :list="namesList" />
  </div>
</template>
