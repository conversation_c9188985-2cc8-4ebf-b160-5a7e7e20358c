export default {
  action: {
    search: 'Search',
    advancedSearch: 'Advanced Search',
    reset: 'Reset',
    cancel: 'Cancel',
    confirm: 'Confirm',
    ok: 'OK',
    selectFile: 'Select File',
    downloadImportTemplate: 'Download Import Template',
    add: 'Add',
    save: 'Save',
    import: 'Import',
    export: 'Export',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    edit: 'Edit',
    back: 'Back',
    close: 'Close',
  },

  table: {
    serial: 'Serial',
    createdTime: 'Created time',
    operation: 'Operation',
  },

  placeholder: {
    search: 'Search',
    select: 'Please select',
    input: 'Please enter',
    all: 'All',
    startDate: 'Start Date',
    endDate: 'End Date',
    startTime: 'Start Time',
    endTime: 'End Time',
  },

  error: {
    thisFieldIsRequired: 'This field is required',
    thisFieldMustBeSelected: 'This field must be selected',
    formatIsIncorrect: 'Format is incorrect',
    privateEmailFormatIsIncorrect: 'Private Email format is incorrect',
    mobileNumberFormatIsIncorrect: 'Mobile Number format is incorrect',
    excelTemplateError: 'Excel template error',
    dataDoesNotExist: 'Data does not exist',
    dataError: 'Data error. Please select again.',
    timeoutError: 'Your request timed out. Please refresh and try again.',
    unknowError: 'Sorry, the program encountered an unknown error, please refresh and try again.',
    noAuth: 'Sorry, you do not have permission to access this page.',
  },

  hint: {
    loading: 'Loading data...',
    noSearchResult: 'No search results',
    deleting: 'Deleting...',
    dataSaved: 'Saved successfully',
    savingFailed: 'Save failed',
    successfullyDeleted: 'Deleted successfully',
    deletionFailed: 'Deletion failed',
    downloadSuccessful: 'Download successful',
    downloadFailed: 'Download failed',
    importSucceeded: 'Import successful',
    importFailed: 'Import failed',
    exportSucceeded: 'Export successful',
    exportFailed: 'Export failed',
    uploadCompleted: 'Upload successful',
    failedToUpload: 'Upload failed',
    success: 'Success',
    failed: 'Failed',
  },

};
