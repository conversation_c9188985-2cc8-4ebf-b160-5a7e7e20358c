import { FlowNodeType } from '@/consts/flow-node-type';
import { InputType } from '@/consts/input-type';
import { JoinType } from '@/consts/join-type';

const {
  FLOW_NODE_TYPE_CONDITION_ENTER,
  FLOW_NODE_TYPE_CONDITION,
} = FlowNodeType;


function copy(data) {
  return JSON.parse(JSON.stringify(data));
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export default class erFun {
  stack: Array<any>;

  popStack: Array<any>;

  vue: any;

  nowId: number;

  constructor(vue) {
    this.stack = [];
    this.popStack = [];
    this.nowId = 0;
    this.vue = vue;
  }

  pushStack(data) {
    this.stack.push(data);
  }

  pushPopStack(data) {
    this.popStack.push(data);
  }

  getId() {
    this.nowId += 1;
    return this.nowId;
  }

  // getDetail 把后台得到的数组结构转为基础树形结构备用
  changeStructrue(arr) {
    let data = {};
    arr.forEach((item) => {
      if (item.nodeType === FLOW_NODE_TYPE_CONDITION) {
        // eslint-disable-next-line
        item.checkVal = []; // 已选择的条件类型
        if (item.processNodeConditionDTOList && item.processNodeConditionDTOList.length > 0) {
          // eslint-disable-next-line no-param-reassign
          item.checkVal = item.processNodeConditionDTOList.map((conditionKind) => {
            const localItem = {
              compareValue: undefined,
              inputType: conditionKind.processOptionInputType,
              customOptionCode: conditionKind.customOptionCode,
              processOptionId: conditionKind.processOptionId,
              name: conditionKind.processOptionName,
              id: conditionKind.id,
              logicType: conditionKind.logicType,
              startBrace: conditionKind.startBrace,
              endBrace: conditionKind.endBrace,
              compareType: conditionKind.compareType,
              processNodeSubmitterConditionDTOList: conditionKind.processNodeSubmitterConditionDTOList,
            };

            switch (conditionKind.processOptionInputType) {
              case InputType.RADIO:
                localItem.compareValue = conditionKind.compareValue;
                break;
              case InputType.CHECKBOX:
                if (conditionKind.compareValue) {
                  localItem.compareValue = conditionKind.compareValue.split(',');
                } else {
                  localItem.compareValue = [];
                }
                break;
              case InputType.NUMBER:
              case InputType.HOUR:
              case InputType.MONEY:
                localItem.compareValue = conditionKind.compareValue;
                break;
              default:
                break;
            }
            return localItem;
          });
        }
      }
      if (item.parentId) {
        // eslint-disable-next-line
        item.parentId = parseInt(item.parentId);
        this.insertNode(data, item);
      } else {
        data = item;
      }
      if (item.id >= this.nowId) {
        this.nowId = item.id + 1;
      }
      // eslint-disable-next-line
      item.post = [];
      // eslint-disable-next-line no-param-reassign
      // item.postIds = [];
      if (item.postIds && item.postIds.length) {
        item.postIds.forEach((postid, index) => {
          item.post.push({
            postId: postid,
            postName: item.postNames[index],
          });
        });
      }
    });
    return data;
  }

  addChildNode(d, obj) {
    // eslint-disable-next-line
    obj.id = this.getId();
    const data = JSON.parse(JSON.stringify(d));
    this.myAddChildNode(data, obj);
    return data;
  }

  // eslint-disable-next-line class-methods-use-this
  delNode(data, obj) {
    // console.log(data, obj);
    if (!obj.parentId) {
      if (obj.next) {
        // eslint-disable-next-line
        data = obj.next;
        // eslint-disable-next-line
        data.parentId = null;
      } else {
        // eslint-disable-next-line
        data = null;
      }
    } else {
      this.myDelChildNode(data, obj, data);
    }
    return data;
  }

  myDelChildNode(d, obj, tree) {
    const data = d;
    if (data.id === obj.parentId) {
      if (obj.nodeType !== FLOW_NODE_TYPE_CONDITION) {
        if (obj.next) {
          // eslint-disable-next-line
          data.next = copy(obj.next);
          // eslint-disable-next-line
          data.next.parentId = data.id;
        } else {
          data.next = null;
        }
      } else {
        // if (data.child.length <= 2) {
        //   this.vue.$message.error('分支节点至少两个！');
        //   return;
        // }
        // 仅有两个分支节点时删除就都删除
        if (data.child.length === 2) {
          data.child.splice(0, 2);
        }
        data.child.forEach((i, k) => {
          if (i.id === obj.id) {
            data.child.splice(k, 1);
          }
        });
        if (data.child.length === 0) {
          this.delNode(tree, data);
        }
      }
    } else {
      if (data.next) {
        this.myDelChildNode(data.next, obj, tree);
      }
      if (data.child) {
        data.child.forEach((i) => {
          this.myDelChildNode(i, obj, tree);
        });
      }
    }
  }

  myAddChildNode(d, obj) {
    const data = d;
    if (obj.nodeType === FLOW_NODE_TYPE_CONDITION_ENTER) {
      // eslint-disable-next-line
      obj.child = [];
      let i = 0;
      while (i < 2) {
        obj.child.push({
          nodeType: FLOW_NODE_TYPE_CONDITION,
          parentId: obj.id,
          id: this.getId(),
          checkVal: [],
          ruleArr: [],
        });
        i += 1;
      }
    }
    if (data.id === obj.parentId) {
      if (obj.nodeType !== FLOW_NODE_TYPE_CONDITION) {
        if (data.next) {
          // eslint-disable-next-line
          obj.next = copy(data.next);
          // eslint-disable-next-line
          obj.next.parentId = obj.id;
          data.next = obj;
        } else {
          data.next = obj;
        }
      } else {
        // eslint-disable-next-line
        obj.checkVal = [];
        // eslint-disable-next-line
        obj.ruleArr = [];
        data.child.push(obj);
      }
    } else {
      if (data.next) {
        this.myAddChildNode(data.next, obj);
      }
      if (data.child) {
        data.child.forEach((i) => {
          this.myAddChildNode(i, obj);
        });
      }
    }
  }

  insertNode(d, item) { // 插入节点
    const data = d;
    if (data.id === item.parentId) {
      if (item.nodeType !== FLOW_NODE_TYPE_CONDITION) { // 不是条件节点
        data.next = item;
      } else { // 是条件节点
        // eslint-disable-next-line
        if (data.child) {
          data.child.push(item);
        } else {
          data.child = [item];
        }
      }
    } else {
      if (data.next) {
        this.insertNode(data.next, item);
      }
      if (data.child) {
        data.child.forEach((i) => {
          this.insertNode(i, item);
        });
      }
    }
  }

  changeBroPos(d, pId, id, cid) { // 更换节点在兄弟集合中的位置
    const data = JSON.parse(JSON.stringify(d));
    // eslint-disable-next-line
    this.myChangeBroPos(data, pId, id, cid);
    return data;
  }

  // eslint-disable-next-line
  myChangeBroPos(d, pId, id, cid) { // 更换节点位置
    const data = d;
    if (data.id === pId) {
      // data.child.
      const obj = data.child.splice(id, 1);
      data.child.splice(cid, 0, obj[0]);
    } else {
      if (data.child) {
        data.child.forEach((i) => {
          this.myChangeBroPos(i, pId, id, cid);
        });
      }
      if (data.next) {
        this.myChangeBroPos(data.next, pId, id, cid);
      }
    }
  }

  replaceNode(d, obj) { // 找到id为obj.id的节点，并替换之
    let data = JSON.parse(JSON.stringify(d));
    if (obj.parentId) {
      // eslint-disable-next-line
      this.myReplaceNode(data, obj);
    } else {
      data = obj;
    }
    return data;
  }

  myReplaceNode(d, obj) {
    const data = d;
    if (data.id === obj.parentId) {
      if (obj.nodeType !== FLOW_NODE_TYPE_CONDITION) {
        // eslint-disable-next-line
        data.next = JSON.parse(JSON.stringify(obj));
      } else {
        data.child.forEach((i, k) => {
          if (i.id === obj.id) {
            data.child.splice(k, 1, obj);
          }
        });
      }
    } else {
      if (data.next) {
        this.myReplaceNode(data.next, obj);
      }
      if (data.child) {
        data.child.forEach((i) => {
          this.myReplaceNode(i, obj);
        });
      }
    }
  }
  // eslint-disable-next-line
  getLastArr(d) {
    const data = copy(d);
    const arr = [];
    this.pushNode(data, arr);
    return arr;
  }

  // 提交时结构化数据
  pushNode(data, arr) {
    const obj = copy(data);
    obj.next = null;
    obj.child = null;
    if (obj.processNodeSettingDTO?.joinType === JoinType.ALL) {
      obj.processNodeSettingDTO.processNodeJoinerDTOList = [];
    }
    // if (obj.checkVal?.length > 0) {
    //   obj.processNodeConditionDTOList = obj.checkVal.map((conditionKind) => {
    //     const item = {
    //       id: conditionKind.id,
    //       name: conditionKind.name,
    //       compareValue: conditionKind.compareValue,
    //       inputType: conditionKind.inputType,
    //       customOptionCode: conditionKind.customOptionCode,
    //       processOptionId: conditionKind.processOptionId,
    //       logicType: conditionKind.logicType,
    //       startBrace: conditionKind.startBrace,
    //       endBrace: conditionKind.endBrace,
    //     };

    //     switch (conditionKind.inputType) {
    //       case InputType.RADIO:
    //         item.compareValue = conditionKind.compareValue;
    //         break;
    //       case InputType.CHECKBOX:
    //         item.compareValue = conditionKind.compareValue.toString();
    //         break;
    //       case InputType.HOUR:
    //       case InputType.NUMBER:
    //       case InputType.MONEY:
    //         item.compareValue = conditionKind.compareValue;
    //         break;
    //       default:
    //         break;
    //     }

    //     return item;
    //   });
    // } else {
    //   obj.processNodeConditionDTOList = null;
    // }
    if (obj.post && obj.post.length) {
      obj.postIds = [];
      obj.post.forEach((itemPost) => {
        obj.postIds.push(itemPost.postId);
      });
    }
    arr.push(obj);
    if (data.next) {
      this.pushNode(data.next, arr);
    }
    if (data.child) {
      // eslint-disable-next-line
      data.child.forEach((i, k) => {
        // eslint-disable-next-line
        i.sortOrder = k + 1;
        this.pushNode(i, arr);
      });
    }
  }
}
