<template>
  <div class="pima-search-advanced-wrapper">
    <div class="content">
      <slot />
    </div>

    <ol class="gap-list-15 operation">
      <li>
        <Button
          type="primary"
          class="pima-btn"
          @click="formSearch.search()"
        >
          {{ $t('common.action.search') }}
        </Button>
      </li>

      <li>
        <Button
          type="primary"
          class="pima-btn"
          ghost
          @click="formSearch.reset()"
        >
          {{ $t('common.action.reset') }}
        </Button>
      </li>

      <li>
        <Button
          type="text"
          class="pima-btn"
          @click="formSearch.showSimpleSearch()"
        >
          {{ $t('common.action.cancel') }}
        </Button>
      </li>
    </ol>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'WrapperSearchAdvanced',

  props: {
    formSearch: {
      type: Object,
      required: true,
    },
  },
});
</script>
