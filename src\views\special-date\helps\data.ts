import { v4 as uuidV4 } from 'uuid';
import { SpecialDateStatus, SpecialDateType } from '@/consts/special-date';

export enum OpenModalType {
  ADD = 'ADD',
  MODIFY = 'MODIFY',
}

export interface TableRowData {
  id:number,
  startDate:string,
  endDate:string,
  name: string,
  type: string,
  isEnable: SpecialDateStatus,
  days:number,
}

interface ModalVisibleType {
  visible:boolean
}


interface TimeRangeType {
  id?:string,
  startDate:string,
  endDate:string
}

export interface FormType {
  id?:number,
  type:SpecialDateType
  name:string,
  time:TimeRangeType [],
  days:number,
  isEnable:SpecialDateStatus | boolean
  holidayDateTool?:{
    datesList:TimeRangeType[]
  }

}

export interface TipsType {
  id:number
}

export type ModalType<T> = ModalVisibleType & {
  model: T
};

export const useTimeRange = ():TimeRangeType => {
  return {
    id: uuidV4(),
    startDate: null,
    endDate: null,
  };
};
export const useFormModal = ():ModalType<FormType> => {
  const model = {
    type: SpecialDateType.HOLIDAY,
    name: null,
    time: [useTimeRange()],
    days: 0,
    isEnable: SpecialDateStatus.ENABLE,
  };
  return {
    model,
    visible: false,
  };
};
