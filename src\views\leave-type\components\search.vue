<script setup lang="ts">
import SearchForSimple from './search/simple.vue';

// eslint-disable-next-line import/order
import { UseQueryTableReturn } from '@/uses/query-table';

const qt = defineModel<UseQueryTableReturn>();
const selectedRowsId = defineModel<number[]>('selectedRowsId');

const emit = defineEmits<{
  'on-batch-copy':[]
}>();

</script>


<template>
  <!-- 简单搜索 -->
  <SearchForSimple
    v-model:selected-rows-id="selectedRowsId"
    v-model="qt"
    @on-batch-copy="emit('on-batch-copy')"
  />
</template>
