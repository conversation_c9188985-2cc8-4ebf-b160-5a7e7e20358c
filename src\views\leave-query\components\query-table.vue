<script setup lang="ts">
import { useRouter } from 'vue-router';

import CTable from '@/components/common/c-table';

import { RouterName as RN } from '@/config/router';
import { namespaceT } from '@/helps/namespace-t';
import { dateFormatSTZ } from '@/helps/date';
import { push } from '@/helps/navigation';
import { handleRowDateText } from '@/helps/leave-time-text';
import { getApprovalStatusText, getApprovalStatusColor } from '@/helps/get-status-text';
import { getApprovalTypeText } from '@/utils/get-text-or-options';
import { getEmptyText } from '@/utils/empty-text';

import { createColumns } from './columns';
import { TableRowData } from '../helps/data';


withDefaults(defineProps<{
  data: TableRowData[],
  loading: boolean,
}>(), {
  data: null,
  loading: false,
});

const router = useRouter();

const selectRows = defineModel<number[]>('selectRows');

const td = namespaceT('dateFormat');
const t = namespaceT('leaveQuery');

const toDetailPage = (row:TableRowData) => {
  push(router, {
    name: RN.QueryDetail,
    params: {
      id: row.id,
    },
  });
};

const onSelectionChange = (selections:TableRowData[]) => {
  selectRows.value = selections.map((item) => item.id);
};

</script>


<template>
  <CTable
    v-bind="$attrs"
    :columns="createColumns()"
    :data="data"
    :loading="loading"
    @on-selection-change="onSelectionChange"
  >
    <!-- 申请单号 -->
    <template #applyOrder="{row}">
      <a @click.prevent="toDetailPage(row)">{{ row.sn }}</a>
    </template>

    <!-- 姓名/学号-->
    <template #nameOrStudId="{ row }">
      <p>{{ getEmptyText(row.userName) }}</p>
      <p>{{ getEmptyText(row.userNo) }}</p>
    </template>

    <!-- 申请类型 -->
    <template #applyType="{row}">
      {{ getApprovalTypeText(row.applyType) }}
    </template>

    <!-- 请假时间 -->
    <template #leaveTime="{row}">
      {{ handleRowDateText(row) }}
    </template>

    <!-- 请假时长 -->
    <template #days="{row}">
      {{ t('content.days',{days:row.leaveDays}) }}
    </template>

    <!-- 状态 -->
    <template #approvalStatus="{row}">
      <span
        :style="`color:${getApprovalStatusColor(row.approvalStatus)}`"
      >
        {{ getApprovalStatusText(row.approvalStatus) }}
        <span />
      </span>
    </template>

    <!-- 最后操作人/时间-->
    <template #operatorAndTime="{ row }">
      <p>{{ row.updateUserName }}</p>
      <p>{{ dateFormatSTZ(row.updateTime, td('dateTime')) }}</p>
    </template>
  </CTable>
</template>
