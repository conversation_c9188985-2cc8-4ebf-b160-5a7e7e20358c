import { LOCAL_BDC_SERVICE_API_BASE_URL } from '@/config/api';
import { valueByLocale } from '@/helps/locale';
import { CommonApi } from '@/api/common/common-api';
import type { DataTags } from '@/consts/data-tags';


export class DataTagApi extends CommonApi {
  typeCode: DataTags;

  constructor(args) {
    super({
      baseURL: LOCAL_BDC_SERVICE_API_BASE_URL,
    });

    this.typeCode = args.typeCode;
  }

  url() {
    return `/services/${process.env.SERVICE_CODE}/service-dict-type/${this.typeCode}/service-dicts`;
  }

  async send() {
    const { model } = await super.send();

    if (!model) {
      return [];
    }

    return model.map((item) => {
      return {
        ...item,
        nameByLocale: valueByLocale(item.name, item.enName),
      };
    });
  }
}
