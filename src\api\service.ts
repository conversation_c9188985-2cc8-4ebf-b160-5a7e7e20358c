import { LOCAL_BDC_SERVICE_API_BASE_URL } from '@/config/api';
import { CommonApi } from '@/api/common/common-api';


export class ServicesApi extends CommonApi {
  constructor() {
    super({
      baseURL: LOCAL_BDC_SERVICE_API_BASE_URL,
    });
  }

  url() {
    return '/services';
  }

  async send() {
    const { data } = await super.send();

    if (!data) {
      return [];
    }

    return data;
  }
}
