import { namespaceT } from '@/helps/namespace-t';
import { createInputRules, createSelectRules } from '@/helps/rules';

const createTimeRangeRules = (rule, value, callback) => {
  const t = namespaceT('common.error');
  if (!value) {
    callback(new Error(t('thisFieldMustBeSelected')));
  } else if (value.notLimit || (value.leaveStartTime && value.leaveEndTime)) {
    callback();
  } else {
    callback(new Error(t('thisFieldMustBeSelected')));
  }
};

const createMaxLeaveAmtPerPeriodRules = (rule, value, callback) => {
  const tm = namespaceT('common.error');
  const t = namespaceT('leaveType.error');
  if (!value.days && !value.limit) {
    if (value.days === 0) {
      callback(new Error(t('mustBeGreaterThanZero')));
    } else {
      callback(new Error(tm('thisFieldIsRequired')));
    }
  } else {
    callback();
  }
};

const createSelectMultipleRules = (rule, value, callback) => {
  const t = namespaceT('common.error');

  if (Array.isArray(value) && value.length > 0) {
    callback();
  } else {
    callback(new Error(t('thisFieldMustBeSelected')));
  }
};


export const createModalFormRules = () => {
  return {
    deptIds: [{
      required: true,
      validator: createSelectMultipleRules,
      trigger: 'change',
    }],
  };
};


export const createModalTypeRules = () => {
  return {
    name: [createInputRules()],
    deptIds: [{
      required: true,
      validator: createSelectMultipleRules,
      trigger: 'change',
    }],

    leaveTime: [{
      required: true,
      validator: createTimeRangeRules,
      trigger: 'change',
    }],
    maxLeaveLength: [createInputRules()],
    maxLeaveAmtPerPeriods: [{
      required: true,
      validator: createMaxLeaveAmtPerPeriodRules,
      trigger: 'change',
    }],

    isEnable: [createSelectRules()],
  };
};


const validatorForName = () => {
  return (rule, value, callback) => {
    const t = namespaceT('common.error');
    if (!value) {
      callback(new Error(t('thisFieldIsRequired')));
      return;
    }

    callback();
  };
};

const validatorForIsEnable = () => {
  const t = namespaceT('common.error');

  return (rule, value, callback) => {
    if (!value) {
      callback(new Error(t('thisFieldMustBeSelected')));
      return;
    }
    callback();
  };
};

const validatorForAttExt = () => {
  const t = namespaceT('common.error');

  return (rule, value, callback) => {
    if (Array.isArray(value) && value.length > 0) {
      callback();
      return;
    }
    callback(new Error(t('thisFieldIsRequired')));
  };
};

const validatorForAttSize = () => {
  const t = namespaceT('leaveType.error');
  const tc = namespaceT('common.error');

  return (rule, value, callback) => {
    if (value > 0) {
      callback();
      return;
    } if (value === 0) {
      callback(new Error(t('mustBeGreaterThanZero')));
      return;
    }
    callback(new Error(tc('thisFieldIsRequired')));
  };
};


export const createLeaveConfigRules = () => {
  return {
    name: [{
      required: true,
      validator: validatorForName(),
      trigger: 'change',
    }],
    isEnable: [{
      required: true,
      validator: validatorForIsEnable(),
      trigger: 'change',

    }],

    attExt: [{
      required: true,
      validator: validatorForAttExt(),
      trigger: 'change',
    }],
    attSize: [{
      required: true,
      validator: validatorForAttSize(),
      trigger: 'change',
    },
    ],
  };
};
