
<script setup lang="ts">
import { computed, watchEffect } from 'vue';

import PickerDayRange from '@/components/common/picker/picker-day-range.vue';
import PimaInput from '@/components/common/pima-input.vue';
import SelectPimaRemoteDepartment from '@/components/common/select/pima-remote-department.vue';
import RadioIsEnable from '../../radio/is-enable.vue';

import { namespaceT } from '@/helps/namespace-t';
import { AddOrModifyTypeModalType } from '@/views/leave-type/helps/data';
import { CommonDepartmentsParams } from '@/consts/common-departments';
import { MaxLength } from '@/consts/max-length';

const emit = defineEmits<{
  'scope-change':[]
}>();

const t = namespaceT('leaveType');
const model = defineModel<AddOrModifyTypeModalType>();


const canToggleCheckTimeLimit = computed(() => {
  const { maxLeaveAmtPerPeriods } = model.value;
  return maxLeaveAmtPerPeriods && !maxLeaveAmtPerPeriods.days;
});


const onChangeMaxLeaveDays = (val) => {
  if (val) {
    model.value.leaveTime.notLimit = false;
  }
};


watchEffect(() => {
  if (model.value.maxLeaveAmtPerPeriods.limit) {
    model.value.maxLeaveAmtPerPeriods.days = null;
  }
});

</script>

<template>
  <!-- 假期名称 -->
  <FormItem
    :label="t('label.typeName')"
    prop="typeName"
  >
    <PimaInput
      v-model.trim="model.name"
      class="w-350"
      clearable
      :max-length="MaxLength.INPUT_MAX_LENGTH"
    />
  </FormItem>

  <!-- 适用范围 -->
  <FormItem
    :label="t('label.scope')"
    prop="deptIds"
  >
    <SelectPimaRemoteDepartment
      v-model="model.deptIds"
      multiple
      is-concat-parent-dept-name
      filterable
      :width="350"
      :parent-code="CommonDepartmentsParams.Student"
      @on-change="emit('scope-change')"
    />
  </FormItem>

  <!-- 可请假时间范围 -->
  <FormItem
    :label="t('label.timeLimitForLeave')"
    prop="leaveTime"
  >
    <div class="time-limit-for-leave">
      <PickerDayRange
        v-model:min="model.leaveTime.leaveStartTime"
        v-model:max="model.leaveTime.leaveEndTime"
        class="datePicker w-300"
        transfer
        :disabled="model.leaveTime.notLimit"
      >
        <template #separator>
          <span class="separator">{{ t('content.to') }}</span>
        </template>
      </PickerDayRange>

      <div class="checkbox-part pima-checkbox-group">
        <Checkbox
          v-model="model.leaveTime.notLimit"
          :disabled="!canToggleCheckTimeLimit"
        >
          {{ t('content.notLimit') }}
        </Checkbox>
      </div>
    </div>
  </FormItem>

  <!-- 最大请假时长 -->
  <FormItem
    :label="t('label.maxDuration.label')"
    prop="code"
  >
    <span class="content-part">
      {{ t('label.maxDuration.content1') }}
    </span>
    <InputNumber
      v-model.trim="model.maxLeaveLength"
      class="w-100 pima-input-number number-input"
      :precision="0"
      :min="0"
      :step="1"
    />
    <span class="content-part">
      {{ t('label.maxDuration.content2') }}
    </span>
  </FormItem>

  <!-- 累计请假时长 -->
  <FormItem
    :label="t('label.cumulativeDuration.label')"
    prop="maxLeaveAmtPerPeriods"
  >
    <div class="maxLeaveAmtPerPeriod">
      <div class="input-part">
        <span class="content-part">
          {{ t('label.cumulativeDuration.content1') }}
        </span>
        <InputNumber
          v-model="model.maxLeaveAmtPerPeriods.days"
          class="w-100 pima-input-number number-input"
          :disabled="model.maxLeaveAmtPerPeriods.limit"
          :precision="0"
          :min="0"
          :step="1"
          @on-change="onChangeMaxLeaveDays"
        />
        <span class="content-part">
          {{ t('label.cumulativeDuration.content2') }}
        </span>
      </div>
      <div class="checkbox-part pima-checkbox-group">
        <Checkbox v-model="model.maxLeaveAmtPerPeriods.limit">
          {{ t('content.notLimit') }}
        </Checkbox>
      </div>
    </div>
  </FormItem>

  <!-- 是否启用该假期 -->
  <FormItem
    :label="t('label.whetherToEnableTheHoliday')"
    prop="isEnable"
  >
    <RadioIsEnable v-model="model.isEnable" />
  </FormItem>
</template>

<style lang="less" scoped>
.datePicker {
  .separator {
    display: inline-block;
    margin: 0 10px;
  }
}

.number-input {
  margin: 0 10px ;
}

.maxLeaveAmtPerPeriod,
.time-limit-for-leave {
  display: flex;
  gap:30px;
}
</style>
