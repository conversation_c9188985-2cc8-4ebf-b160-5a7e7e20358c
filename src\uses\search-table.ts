import { reactive } from 'vue';
import _ from 'lodash';
import { useQueryTable } from './query-table';
import { useShowState } from './show-state';


export function useSearchTable(config) {
  if (!('load' in config)) {
    throw new Error('[useSearchTable] Missing configuration “load”');
  }

  const createSearchSimpleModel = _.get(config, 'createSearchSimpleModel', () => ({}));
  const createSearchAdvancedModel = _.get(config, 'createSearchAdvancedModel', () => ({}));
  const searchSimple = reactive(createSearchSimpleModel());
  const searchAdvanced = reactive(createSearchAdvancedModel());
  const showState = useShowState();

  function load(option = {}) {
    if (!showState.shown.value) {
      _.merge(searchAdvanced, option);
    } else {
      _.merge(searchSimple, option);
    }

    return config.load(option);
  }

  const queryTable = useQueryTable({
    ...config,
    load,
  });

  function restore() {
    showState.show();
    Object.assign(searchAdvanced, createSearchAdvancedModel());
    const option = _.clone(searchSimple);
    delete option.page;
    const page = _.clone(searchSimple.page);
    queryTable.restore(option, page);
  }


  return {
    searchSimple,
    searchAdvanced,
    showState,
    queryTable,
    restore,
  };
}
