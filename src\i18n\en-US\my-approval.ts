import { Status } from '@/consts/status';

export default {
  search: {
    label: {
      approvalStatus: 'Approval status',
      college: 'college',
      leaveType: 'Leave type',
      applyOrder: 'Application number',
      applier: 'Applicant',
      applyTime: 'application time',
    },
    placeholder: {
      keyword: 'Please enter your name and student ID to search',
      applier: 'Chinese name, student ID, account number',
    },
  },

  columns: {
    applyOrder: 'Application number',
    nameOrStudId: 'Name/Student ID',
    college: 'college',
    applyTime: 'application time',
    leaveType: 'Leave type',
    leaveTime: 'Leave time',
    leaveDuration: 'duration of leave',
    leaveReason: 'Reason for leave',
    approvalStatus: 'Approval status',
    operation: 'operation',
  },

  label: {
    allSelectAndData: 'Select All, a total of {count} data',
  },

  content: {
    days: '{days} days',
    hours: '{hours} hours',
  },

  actions: {
    batchApproval: 'Batch approval',
    agree: 'agree',
    reject: 'refuse',
  },

  status: {
    [Status.PASS]: 'Approved',
    [Status.PENDING]: 'Pending approval',
    [Status.REJECT]: 'Approval failed',
  },

  modal: {
    title: {
      agree: 'Agree to approve',
      reject: 'Refuse approval',
      batch: 'Batch approval',
    },
    actions: {
      submit: 'submit',
      cancel: 'cancel',
      confirm: '@:common.action.confirm',
    },
    label: {
      agreeRemark: 'Approval comments',
      rejectRemark: 'Opinion that the approval was not approved',
      approvalResult: 'Approval result',
    },
    placeholder: 'no more than {num} characters',
    hints: {
      remark: 'Please provide your approval comments',
    },
    content: {
      // TODO:待产品确认是否调整
      approval: '是否确认审批此假期？',
    },
  },
};
