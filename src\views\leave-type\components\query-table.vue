<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import PopTipNameList from '@/components/biz/popTip/department.vue';

import { CopyApi } from '@/api/leave-type/copy';
import { RouterName as RN } from '@/config/router';
import { namespaceT } from '@/helps/namespace-t';
import { push } from '@/helps/navigation';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { EmptyText } from '@/consts/empty-text';
import { getEnableStatusText } from '@/utils/leave-type';
import { getEmptyText } from '@/utils/empty-text';
import PopTipTypeName from './poptip/leave-type-name.vue';
import ModalTypeModify from './modal/modify-type.vue';

import { createColumns } from './columns';
import { TableRowData } from '../helps/data';

withDefaults(defineProps<{
  data: TableRowData[],
  loading: boolean,
}>(), {
  data: null,
  loading: false,
});

const emit = defineEmits<{
  'on-reload':[]
}>();

const router = useRouter();

const t = namespaceT('leaveType');
const vm = getCurrentInstance();
const modalTypeRef = ref(null);
const modalTypeId = ref<number>(null);

const onCopy = async (row:TableRowData) => {
  try {
    const params = {
      deptIds: [...row.deptIds],
      leaveTypeIdList: [row.id],
    };
    const api = new CopyApi({});
    api.params = params;
    await api.send();
    emit('on-reload');
    openToastSuccess(t('hint.successCopy'));
  } catch (error) {
    openToastError(error.message);
  }
};

const toLeaveConfigPage = (row:TableRowData) => {
  push(router, {
    name: RN.LeaveConfig,
    params: {
      id: row.id,
    },
  });
};

const onOpenModalType = (row:TableRowData) => {
  modalTypeId.value = row.id;
  modalTypeRef.value.onOpen();
};


const actions = [
  {
    label: t('actions.copy'),
    triggerEvent: onCopy,
    can: vm.proxy.$can((P) => P.LeaveType.Copy),
  },
  {
    label: t('actions.modify'),
    triggerEvent: onOpenModalType,
    can: vm.proxy.$can((P) => P.LeaveType.Modify),
  },
  {
    label: t('actions.leaveConfig'),
    triggerEvent: toLeaveConfigPage,
  },
];

const getMaxCumulativeLeaveDuration = (row:TableRowData) => {
  if (row.isLimitLeaveAmt) {
    return row.maxLeaveAmtPerPeriod
      ? t('content.day', { day: row.maxLeaveAmtPerPeriod })
      : EmptyText;
  }
  return t('content.notLimit');
};


</script>


<template>
  <CTable
    v-bind="$attrs"
    :columns="createColumns()"
    :data="data"
    :loading="loading"
  >
    <!-- 单次最大请假时长 -->
    <template #maxLeaveDurationForSignal="{ row }">
      {{
        row.maxLeaveLength?
          t('content.day', { day: row.maxLeaveLength }):
          EmptyText
      }}
    </template>

    <!-- 累计最多请假时长 -->
    <template #maxCumulativeLeaveDuration="{ row }">
      {{ getMaxCumulativeLeaveDuration(row) }}
    </template>

    <!-- 适用范围 -->
    <template #scope="{row}">
      <PopTipNameList :list="row.deptNames||[]" />
    </template>

    <!-- 是否启用 -->
    <template #isEnabled="{ row }">
      {{ getEnableStatusText(row.isEnable) }}
    </template>

    <!-- 假期名称 -->
    <template #leaveSubTypeName="{ row }">
      <template v-if="row.leaveSubTypes">
        <PopTipTypeName :type-list="row.leaveSubTypes" />
      </template>

      <template v-else>
        {{ getEmptyText() }}
      </template>
    </template>

    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>

  <ModalTypeModify
    :id="modalTypeId"
    ref="modalTypeRef"
    @save-success="emit('on-reload')"
  />
</template>
