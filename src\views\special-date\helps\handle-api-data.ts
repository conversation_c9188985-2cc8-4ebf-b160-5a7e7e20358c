import _ from 'lodash';

import { SpecialDateStatus } from '@/consts/special-date';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { FormType } from './data';

export const handleAddData = (model:FormType) => {
  const td = namespaceT('dateFormat');
  const obj = _.cloneDeep(model);

  obj.isEnable = obj.isEnable === SpecialDateStatus.ENABLE;

  const holidayDateTool = {
    datesList: obj.time.map((item) => {
      return {
        startDate: dateFormatSTZ(item.startDate, td('date')),
        endDate: dateFormatSTZ(item.endDate, td('date')),
      };
    }),
  };

  obj.holidayDateTool = holidayDateTool;

  return _.omit(obj, ['time', 'endDate', 'startDate']);
};

export const handleModifyData = (model:FormType) => {
  const td = namespaceT('dateFormat');
  const obj = _.cloneDeep(model);


  obj.isEnable = obj.isEnable === SpecialDateStatus.ENABLE;

  const time = obj.time[0];
  Object.assign(obj, {
    startDate: dateFormatSTZ(time.startDate, td('date')),
    endDate: dateFormatSTZ(time.endDate, td('date')),
  });

  return _.omit(obj, ['time']);
};
