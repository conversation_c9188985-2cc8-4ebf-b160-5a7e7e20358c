<script setup lang="ts">
import { onMounted, ref } from 'vue';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
// eslint-disable-next-line import/order
import QueryTable from '../components/query-table.vue';
// eslint-disable-next-line import/order
import Search from '../components/search.vue';

import { TableListApi } from '@/api/leave-query/list';
import { openToastError } from '@/helps/toast';
import { useQueryTable } from '@/uses/query-table';

import { handleListData } from '../helps/handle-api-data';
import { createSimpleSearchModel, createAdvanceSearchModel } from '../helps/data';


async function loadData(option) {
  try {
    const params = option || {};

    const api = new TableListApi({});
    api.params = handleListData(params);
    const res = await api.send();
    return res;
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
}

const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSimpleSearchModel(),
  advancedSearchModel: createAdvanceSearchModel(),
});


const selectRows = ref<number[]>([]);


onMounted(() => {
  qt.load();
});

</script>


<template>
  <Search
    v-model="qt"
    :select-rows="selectRows"
  />

  <TableScroll>
    <QueryTable
      v-model:select-rows="selectRows"
      :data="qt.table.data"
      :loading="qt.table.loading"
      :page="qt.page"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>
</template>
