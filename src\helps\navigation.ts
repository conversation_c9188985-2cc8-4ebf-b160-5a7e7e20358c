import _ from 'lodash';
import type { Router } from 'vue-router';
import { RouterName as RN, FROM_PARAM_NAME_KEY, FROM_ROUTE_NAME_KEY } from '@/config/router';


export function beforeEach(to, from, next) {
  Object.assign(to.meta, {
    [FROM_ROUTE_NAME_KEY]: from.name,
  });

  next();
}

export function isCurrentRoot(router: Router) {
  return RN.Root === router.currentRoute.value.name;
}

export function backToRoot(router: Router) {
  router.replace({ name: RN.Root });
}

export function isCurrentForbidden(router: Router) {
  return RN.Forbidden === router.currentRoute.value.name;
}

export function goForbidden(router: Router) {
  router.replace({ name: RN.Forbidden });
}

export function canGoBack(router: Router) {
  // 是否存在from值，如果有值才可以操作浏览器后退
  const from = _.get(router.currentRoute, `query.${FROM_PARAM_NAME_KEY}`, null);
  return !_.isNil(from);
}

export function goBack(router: Router) {
  const fromRouteName = _.get(router.currentRoute, `meta.${FROM_ROUTE_NAME_KEY}`);
  const queryFromRouteName = _.get(router.currentRoute, `query.${FROM_PARAM_NAME_KEY}`, null);
  // 会记录fromRouteName在route的meta里，如果fromRouteName是null/Root，则表示是当前为浏览器打开的第一个URL
  // 如果不是第一个URL，则可以直接操作浏览器后退
  if (![null, RN.Root].includes(fromRouteName)) {
    router.back();
  // 如果是第一个URL，无法正常操作浏览后退，则使用replace方法替换，需指定queryFromRouteName才生效
  } else if (!_.isNil(queryFromRouteName)) {
    router.replace({
      name: queryFromRouteName,
    });
  }
}

export const push = (router, { name, query = {}, params = {} }) => {
  router.push({
    name,
    query: {
      ...query,
      [FROM_PARAM_NAME_KEY]: router.currentRoute.value.name,
    },
    params,
  });
};
