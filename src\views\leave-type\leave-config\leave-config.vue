<!-- eslint-disable import/order -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import PageWrapper from '../components/page-wrapper.vue';
import FormDetail from '../components/form/leave-config.vue';

import { CheckCannotDeleteApi } from '@/api/leave-type/config/check';
import { GetDetailApi } from '@/api/leave-type/config/detail';
import { ModifyApi } from '@/api/leave-type/config/modify';
import { namespaceT } from '@/helps/namespace-t';
import { goBack } from '@/helps/navigation';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { createLeaveConfigItem, createLeaveConfigModel, LeaveConfigModelType, LeaveConfigType } from '../helps/data';
import { handleConfigModifyData } from '../helps/handle-api-data';


const router = useRouter();
const route = useRoute();

const t = namespaceT('leaveType');
const tm = namespaceT('common');
const te = namespaceT('errors.leaveType');
const pageLoading = ref(false);
const loading = ref(false);
const formRef = ref();
const model = ref<LeaveConfigType>(createLeaveConfigModel());
const { id } = route.params;


const back = () => goBack(router);
const changeLoadingStatus = async () => {
  loading.value = !loading.value;
};

const onSubmit = async () => {
  try {
    changeLoadingStatus();
    const api = new ModifyApi({ id });
    api.data = handleConfigModifyData(model.value);
    await api.send();
    openToastSuccess(tm('hint.dataSaved'));
    back();
  } catch (error) {
    openToastError(error.message);
  } finally {
    changeLoadingStatus();
  }
};

const onValidate = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      onSubmit();
    }
  });
};

const onAddModalItem = () => {
  model.value.list.push(createLeaveConfigItem());
};

const onRemoveModalItem = async (removeItem: LeaveConfigModelType) => {
  if (!removeItem.id) {
    model.value.list = model.value.list.filter((item) => item.uuid !== removeItem.uuid);
    return;
  }

  try {
    const api = new CheckCannotDeleteApi({ id: removeItem.id });
    const res = await api.send();
    if (res.model) {
      model.value.list = model.value.list.filter((item) => item.uuid !== removeItem.uuid);
    } else {
      openToastError(te('ALREADY_EXISTS'));
    }
  } catch (error) {
    openToastError(error.message);
  }
};

const onGetDetail = async () => {
  try {
    pageLoading.value = true;
    const api = new GetDetailApi({ id });
    const res = await api.send();
    if (res) {
      model.value.list = res;
    }
  } catch (error) {
    openToastError(error.message);
  } finally {
    pageLoading.value = false;
  }
};

onMounted(() => {
  onGetDetail();
});

</script>


<template>
  <TitleBar
    :title="t('title.detail')"
    go-back
    @go-back="back"
  />
  <PageWrapper>
    <WrapperForm
      :loading="pageLoading"
      action
    >
      <FormDetail
        ref="formRef"
        v-model="model"
        @on-add="onAddModalItem"
        @on-remove="onRemoveModalItem"
      />

      <template #action>
        <div class="action-wrapper">
          <Button
            class="pima-btn"
            :disabled="loading"
            @click="back"
          >
            {{ tm('action.cancel') }}
          </Button>

          <Button
            class="pima-btn"
            type="primary"
            :loading="loading"
            @click="onValidate"
          >
            {{ tm('action.save') }}
          </Button>
        </div>
      </template>
    </WrapperForm>
  </PageWrapper>
</template>

<style lang="less" scoped>
.action-wrapper{
  .pima-btn{
    &:not(:last-child){
      margin-right: 20px;
    }
  }
}
</style>
