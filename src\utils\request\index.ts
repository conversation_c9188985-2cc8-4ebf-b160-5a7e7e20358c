import axios from 'axios';
import qs from 'qs';
import jsonBigInt from 'json-bigint';

import { interceptorNullFilter } from './interceptors/null-filter';
import { interceptorAjax } from './interceptors/ajax';
import { interceptorSignature } from './interceptors/signature';
import { interceptorNonce } from './interceptors/nonce';
import { interceptorLocale } from './interceptors/locale';


function readBlobError(reader) {
  return new Promise(((resolve) => {
    // eslint-disable-next-line
    reader.onload = () => {
      const data = JSON.parse(reader.result);
      const error = new Error(data.errorMsg);
      error.code = data.errorCode;
      error.response = { data };
      resolve(error);
    };
  }));
}

export function createRequest({ baseURL, timeout, responseType, headers, salt, locale }) {
  const xhr = axios.create({
    baseURL,
    responseType,
    headers,
    timeout: (Number.isFinite(timeout) ? timeout : 20 * 1000),
    withCredentials: true,
    maxRedirects: 0,
    paramsSerializer(params) {
      return qs.stringify(params, { indices: false });
    },
    transformResponse: [
      (data) => {
        try {
          return jsonBigInt({ storeAsString: true }).parse(data);
        } catch (error) {
          return data;
        }
      },
    ],
  });

  xhr.interceptors.request.use(
    interceptorNullFilter,
  );

  xhr.interceptors.request.use(
    interceptorAjax,
  );

  xhr.interceptors.request.use(
    interceptorSignature({ salt }),
  );

  xhr.interceptors.request.use(
    interceptorNonce,
  );

  xhr.interceptors.request.use(
    interceptorLocale({ locale }),
  );

  xhr.interceptors.response.use(
    async (response) => {
      const { data } = response;

      if (data instanceof Blob) {
        // Blob报错情况处理
        if (!data.type.includes('application/json')) {
          return response;
        }

        const reader = new FileReader();
        reader.readAsText(data);
        const error = await readBlobError(reader);
        throw error;
      }

      // 响应状态为失败则抛错
      if (!data.success) {
        const error = new Error(data.errorMsg);
        error.code = data.errorCode;
        error.response = response;
        throw error;
      }

      return data;
    },
  );

  return xhr;
}
