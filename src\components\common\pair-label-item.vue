<template>
  <div class="pima-pair-label-item">
    <div
      v-if="label"
      class="label"
      :class="labelClass"
      v-text="label"
    />
    <div class="content">
      <slot />
    </div>
  </div>
</template>


<script lang='ts'>
import { computed, defineComponent, toRef } from 'vue';


export default defineComponent({
  name: 'PairLabelItem',

  props: {
    label: {
      type: String,
      default: '',
    },

    noColon: {
      type: Boolean,
      default: true,
    },

    preLine: Boolean,
  },

  setup(props) {
    const noColon = toRef(props, 'noColon');
    const preLine = toRef(props, 'preLine');

    const labelClass = computed(() => {
      return {
        'no-colon': noColon.value,
        'pre-line': preLine.value,
      };
    });

    return {
      labelClass,
    };
  },
});
</script>

<style lang="less" scoped>
.pima-pair-label-item .label {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}
</style>
