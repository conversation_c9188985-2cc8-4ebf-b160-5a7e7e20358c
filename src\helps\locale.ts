import { i18n } from '@/i18n';
import { Locale } from '@/config/locale';


export function currentLocale(): Locale {
  return i18n.global.locale.value;
}

type TextFn = string | number | (() => string | number);
export function valueByLocale(chnFn: TextFn, engFn: TextFn): string | number {
  if (currentLocale() === Locale.ZH_CN) {
    return typeof chnFn === 'function' ? chnFn() : chnFn;
  }

  return typeof engFn === 'function' ? engFn() : engFn;
}

export function textByLocale(chnText: string | number, engText: string | number, fallback = false): string | number {
  if (fallback) {
    return valueByLocale(() => (chnText || engText), () => (engText || chnText));
  }

  return valueByLocale(chnText, engText);
}
