import { namespaceT } from '@/helps/namespace-t';
import { BaseError } from '@/errors/base-error';
import { CommonApi } from '../common/common-api';
// NO_EXISTS:数据不存在，LEAVE_TYPE_MISMATCH:请假类型不匹配，

enum ErrorCode {
  NO_EXISTS = 'NO_EXISTS',
  LEAVE_TYPE_MISMATCH = 'LEAVE_TYPE_MISMATCH',
}

export class AddApi extends CommonApi {
  url() {
    return '/leave-types';
  }

  method(): string {
    return 'POST';
  }

  async send() {
    try {
      await super.send();
    } catch (error) {
      const t = namespaceT('errors.leaveType');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
        case ErrorCode.LEAVE_TYPE_MISMATCH:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
