/* eslint-disable no-undef */
// Vue SSR相关配置

module.exports = function createConfig() {
  let publicPath = process.env.PUBLIC_PATH || '/';
  if (publicPath.lastIndexOf('/') !== publicPath.length - 1) {
    publicPath += '/';
  }

  const PUBLIC_PATH = publicPath;
  const HOT_CLIENT_WEB_SOCKET_PORT = process.env.HOT_CLIENT_WEB_SOCKET_PORT || 8090;
  const TEMPLATE_PATH = './index.template.html';
  const VUE_SSR_SERVER_BUNDLE_PATH = './dist/vue-ssr-server-bundle.json';
  const VUE_SSR_CLIENT_MANIFEST_PATH = './dist/vue-ssr-client-manifest.json';

  // 语言
  const FALLBACK_LOCALE = process.env.FALLBACK_LOCALE || 'zh_CN';

  // 认证相关
  const LOGIN_STATUS_LOGGED_IN_KEY = 'appLoggedIn';
  const LOGIN_STATUS_USERNAME_KEY = 'appUsername';

  // Redis相关配置
  const REDIS_KEY_PREFIX = process.env.REDIS_KEY_PREFIX || 'pkusz:student-leave:'; // 存储时的键值前缀
  const REDIS_TTL = 60 * 60 * 2; // Session有效期為2h
  // const REDIS_TTL = 60 * 5; // 測試暫時修改為5分鐘

  // 静态目录在生产环境下的缓存maxAge
  const STATIC_MAX_AGE = 365 * 24 * 60 * 60 * 1000; // 单位为ms，共1年;

  // Cookie相关配置
  // Cookie加密串，每个应用值应不同
  const COOKIE_SECRET = 'pkusz_student_leave';
  // 用於 Session ID 的 Session 鍵值
  const SESSION_ID_COOKIE_KEY = process.env.SESSION_ID_COOKIE_KEY || 'pkusz.student-leave.sid';
  // 用於 Access Token 的 Cookie 鍵值
  const ACCESS_TOKEN_COOKIE_KEY = process.env.ACCESS_TOKEN_COOKIE_KEY || 'pkusz.student-leave.at';
  // 用於 i18n 的 Cookie 鍵值，全局配置，必填
  const LOCALE_COOKIE_KEY = process.env.LOCALE_COOKIE_KEY || 'pima.locale';

  // 接口相关配置
  // 本地API，会转发至API服务器
  const LOCAL_BDC_CORE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-core-api`; // 公共接口API
  const LOCAL_STUDENT_LEAVE_API_BASE_URL = `${PUBLIC_PATH}x-student-leave-api`; // 请假接口 API
  const LOCAL_BDC_AUTH_API_BASE_URL = `${PUBLIC_PATH}x-bdc-auth-api`; // 统一权限接口 API
  const LOCAL_BDC_ARCH_API_BASE_URL = `${PUBLIC_PATH}x-bdc-arch-api`; // 统一数据接口 API
  const LOCAL_BDC_SERVICE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-service-api`; // 统一应用接口 API
  const LOCAL_BDC_DFS_API_BASE_URL = `${PUBLIC_PATH}x-bdc-dfs-api`; // 附件管理接口 API
  const LOCAL_BDC_EXPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-export-api`; // 导出组件接口 API
  const LOCAL_BDC_IMPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-import-api`; // 导入组件接口 API

  return {
    PUBLIC_PATH,
    HOT_CLIENT_WEB_SOCKET_PORT,
    TEMPLATE_PATH,
    VUE_SSR_SERVER_BUNDLE_PATH,
    VUE_SSR_CLIENT_MANIFEST_PATH,

    FALLBACK_LOCALE,

    LOGIN_STATUS_LOGGED_IN_KEY,
    LOGIN_STATUS_USERNAME_KEY,

    REDIS_KEY_PREFIX,
    REDIS_TTL,

    STATIC_MAX_AGE,

    COOKIE_SECRET,
    SESSION_ID_COOKIE_KEY,
    ACCESS_TOKEN_COOKIE_KEY,
    LOCALE_COOKIE_KEY,

    LOCAL_BDC_CORE_API_BASE_URL,
    LOCAL_STUDENT_LEAVE_API_BASE_URL,
    LOCAL_BDC_AUTH_API_BASE_URL,
    LOCAL_BDC_ARCH_API_BASE_URL,
    LOCAL_BDC_SERVICE_API_BASE_URL,
    LOCAL_BDC_DFS_API_BASE_URL,
    LOCAL_BDC_EXPORT_API_BASE_URL,
    LOCAL_BDC_IMPORT_API_BASE_URL,
  };
};
