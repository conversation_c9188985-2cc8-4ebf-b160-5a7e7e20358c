import { Status } from '@/consts/status';

export default {
  search: {
    label: {
      college: 'college',
      leaveType: 'Leave type',
      applyOrder: 'Application number',
      approvalStatus: 'Approval status',
      applier: 'Applicant',
      applyTime: 'application time',
      approver: 'approver',
    },
    placeholder: {
      keyword: 'Please enter your name and student ID to search',
      applier: 'Name, student ID, account number',
    },
  },

  columns: {
    applyOrder: 'Application number',
    nameOrStudId: 'Name/Student ID',
    college: 'college',
    leaveType: 'Leave type',
    leaveTime: 'Leave time',
    leaveDuration: 'duration of leave',
    leaveReason: 'Reason for leave',
    approvalStatus: 'Approval status',
    operatorAndTime: 'Last operator/time',
  },

  content: {
    days: '{days} days',
    hours: '{hours} hours',
  },

  actions: {
    export: 'export',
  },

  status: {
    [Status.PASS]: 'Approved',
    [Status.PENDING]: 'Pending approval',
    [Status.REJECT]: 'Approval failed',
  },
};
