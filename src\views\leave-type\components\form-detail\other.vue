<!-- eslint-disable import/order -->
<script setup lang="ts">
import PimaInput from '@/components/common/pima-input.vue';
import SelectAttachmentRequirements from '../select/attachment-requirements.vue';
import CheckboxFileFormat from '../checkbox/file-format.vue';
import RadioIsEnable from '../radio/is-enable.vue';

import { IsNeedAttachment } from '@/consts/leave-type';
import { MaxLength } from '@/consts/max-length';
import { namespaceT } from '@/helps/namespace-t';

import { LeaveConfigModelType } from '../../helps/data';
import { createLeaveConfigRules } from '../../helps/rules';


withDefaults(defineProps<{
  index:number
}>(), {
  index: 0,
});

const t = namespaceT('leaveType');
const model = defineModel<LeaveConfigModelType>();

const rules = createLeaveConfigRules();


</script>

<template>
  <!-- 假期名称 -->
  <FormItem
    :label="t('label.name')"
    :prop="`list.${index}.name`"
    :rules="rules.name"
  >
    <Input
      v-model="model.name"
      class="w-350 pima-input"
      clearable
      :max-length="MaxLength.INPUT_MAX_LENGTH"
    />
  </FormItem>

  <!-- 假期编码 -->
  <FormItem
    :label="t('label.code')"
    :prop="`list.${index}.code`"
  >
    <PimaInput
      v-model.trim="model.code"
      class="w-350"
      disabled
    />
  </FormItem>

  <!-- 附件要求 -->
  <FormItem
    :label="t('label.attachmentRequirements')"
    :prop="`list.${index}.isNeedAtt`"
  >
    <SelectAttachmentRequirements
      v-model="model.isNeedAtt"
      class="w-350"
    />
  </FormItem>

  <template v-if="model?.isNeedAtt === IsNeedAttachment.YES">
    <!-- 附件格式 -->
    <FormItem
      :label="t('label.format')"
      :prop="`list.${index}.attExt`"
      :rules="rules.attExt"
    >
      <CheckboxFileFormat v-model="model.attExt" />
    </FormItem>

    <!-- 附件大小限制 -->
    <FormItem
      :label="t('label.LimitSize.label')"
      :prop="`list.${index}.attSize`"
      :rules="rules.attSize"
    >
      <span class="content-part">
        {{ t('label.LimitSize.content1') }}
      </span>

      <InputNumber
        v-model="model.attSize"
        class="w-100 pima-input-number number-input"
        :precision="0"
        :min="0"
        :step="1"
      />

      <span class="content-part">
        {{ t('label.LimitSize.content2') }}
      </span>
    </FormItem>
  </template>

  <!-- 休假说明 -->
  <FormItem
    :label="t('label.leaveInstructions')"
    :prop="`list.${index}.content`"
  >
    <PimaInput
      v-model="model.content"
      class="w-400"
      type="textarea"
      :rows="4"
    />
  </FormItem>

  <!-- 是否启用该假期 -->
  <FormItem
    :label="t('label.whetherToEnableTheHoliday')"
    :prop="`list.${index}.isEnable`"
    :rules="rules.isEnable"
  >
    <RadioIsEnable v-model="model.isEnable" />
  </FormItem>
</template>

<style lang="less" scoped>
.number-input{
  margin: 0 10px ;
}
</style>
