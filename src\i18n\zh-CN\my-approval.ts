import { Status } from '@/consts/status';

export default {
  search: {
    label: {
      approvalStatus: '审批状态',
      college: '学院',
      leaveType: '请假类型',
      applyOrder: '申请单号',
      applier: '申请人',
      applyTime: '申请时间',
      revokeLeaveStatus: '销假状态',
      approvalType: '审批类型',
    },

    placeholder: {
      keyword: '请输入姓名、学号进行搜索',
      applier: '中文姓名、学号、账号',
    },

  },

  columns: {
    applyOrder: '申请单号',
    nameOrStudId: '姓名/学号',
    college: '学院',
    approvalType: '审批类型',
    applyTime: '申请时间',
    leaveType: '请假类型',
    leaveTime: '请假时间',
    leaveDuration: '请假时长',
    leaveReason: '请假事由',
    approvalStatus: '审批状态',
    operation: '操作',
  },

  label: {
    allSelectAndData: '全选,共{count}条数据',
  },
  content: {
    days: '{days}天',
    hours: '{hours}小时',
  },

  actions: {
    batchApproval: '批量审批',
    agree: '同意',
    reject: '拒绝',
  },

  status: {
    [Status.PASS]: '审批通过',
    [Status.PENDING]: '待审批',
    [Status.REJECT]: '审批不通过',
  },


  modal: {
    title: {
      agree: '同意审批',
      reject: '拒绝审批',
      batch: '批量审批',
    },
    actions: {
      submit: '提交',
      cancel: '取消',
      confirm: '@:common.action.confirm',
    },
    label: {
      agreeRemark: '审批通过意见',
      rejectRemark: '审批不通过意见',
      approvalResult: '审批结果',
    },
    placeholder: '不超过{num}个字',
    hints: {
      remark: '请输入审批意见',
    },
    content: {
      leave: '是否确认同意该请假申请?',
      revokeLeave: '是否确认同意该销假申请?',
    },
  },

};
