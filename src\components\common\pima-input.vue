<template>
  <Input
    :class="getClasses()"
    :type="type"
    :maxlength="inMaxLength"
    @input="onInput"
  />
</template>


<script lang='ts'>
import { defineComponent } from 'vue';


const InputType = Object.freeze({
  TEXT: 'text',
  PASSWORD: 'password',
  TEXTARER: 'textarea',
  URL: 'url',
  EMAIL: 'email',
  DATE: 'date',
  NUMBER: 'number',
  TEL: 'tel',
});


export default defineComponent({
  name: 'PimaInput',

  props: {
    type: {
      type: String,
      default: InputType.TEXT,
    },

    maxlength: {
      type: Number,
      default: null,
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const isTextarea = props.type === InputType.TEXTARER;
    const maxLengthByType = isTextarea ? 500 : 255;
    const inMaxLength = props.maxlength ? props.maxlength : maxLengthByType;

    function getClasses() {
      switch (props.type) {
        case InputType.TEXTARER:
          return 'pima-input-type-textarea';

        default:
          return 'pima-input';
      }
    }

    function onInput(val) {
      switch (props.type) {
        case InputType.TEXT:
          emit('update:modelValue', val ? val.target.value.trim() : val);
          break;
        default:
          emit('update:modelValue', val.target.value);
      }
    }

    return {
      inMaxLength,

      onInput,
      getClasses,
    };
  },
});
</script>
