
<script setup lang="ts">
import { computed } from 'vue';

import { getFileFormatOptions } from '@/utils/leave-type';

const options = computed(() => getFileFormatOptions());

</script>

<template>
  <CheckboxGroup
    class="pima-checkbox-group"
    v-bind="$attrs"
    v-on="$attrs"
  >
    <Checkbox
      v-for="item in options"
      :key="item.value"
      :label="item.value"
    >
      <span>{{ item.label }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>
